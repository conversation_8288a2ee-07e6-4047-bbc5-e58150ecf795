#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sistema de Reportes de Voicebot
Script principal que orquesta todo el proceso de generación de reportes
"""
import sys
import os
import argparse
from datetime import datetime

# Agregar el directorio actual al path para imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.settings import get_current_date, FILE_CONFIG, EMAIL_CONFIG
from src.database.mongo_client import MongoDBClient
from src.log_processor.asterisk_parser import AsteriskLogParser
from src.log_processor.call_analyzer import AnalizadorLlamadas
from src.reports.excel_generator import ExcelReportGenerator
from src.reports.email_sender import EmailSender
from src.utils.docker_utils import DockerManager
from src.utils.file_utils import file_exists


class VoicebotReporter:
    """Clase principal para generar reportes de Voicebot"""
    
    def __init__(self, fecha: str = None):
        self.fecha = fecha or get_current_date()
        self.mongo_client = None
        self.log_parser = None
        self.call_analyzer = None
        self.excel_generator = None
        self.email_sender = None
        
        print(f"Iniciando reporte para fecha: {self.fecha}")
    
    def initialize_components(self):
        """Inicializa todos los componentes necesarios"""
        try:
            print("Inicializando componentes...")
            
            # Inicializar cliente MongoDB
            self.mongo_client = MongoDBClient()
            
            # Inicializar procesadores de logs
            self.log_parser = AsteriskLogParser()
            self.call_analyzer = AnalizadorLlamadas()
            
            # Inicializar generadores de reportes
            self.excel_generator = ExcelReportGenerator()
            self.email_sender = EmailSender()
            
            print("Componentes inicializados exitosamente")
            return True
            
        except Exception as e:
            print(f"Error inicializando componentes: {e}")
            return False
    
    def copy_asterisk_logs(self) -> bool:
        """Copia logs de Asterisk desde Docker"""
        print("Copiando logs de Asterisk...")
        
        # Verificar estado del contenedor
        if not DockerManager.check_container_status():
            print("Advertencia: El contenedor de Asterisk no está ejecutándose")
            return False
        
        # Copiar logs
        success = DockerManager.copy_asterisk_logs()
        if success:
            print("Logs copiados exitosamente")
        else:
            print("Error copiando logs")
        
        return success
    
    def process_logs(self) -> bool:
        """Procesa los logs y genera archivo JSON"""
        print("Procesando logs de Asterisk...")
        
        # Verificar que el archivo de logs existe
        if not file_exists(FILE_CONFIG['log_file_path']):
            print(f"Error: Archivo de logs no encontrado: {FILE_CONFIG['log_file_path']}")
            return False
        
        # Procesar logs y generar JSON
        success = self.log_parser.guardar_resultados_json()
        if success:
            print("Logs procesados exitosamente")
        else:
            print("Error procesando logs")
        
        return success
    
    def generate_report(self, send_email: bool = True) -> str:
        """
        Genera el reporte completo
        
        Args:
            send_email: Si enviar el reporte por email
            
        Returns:
            Ruta del archivo Excel generado
        """
        print(f"Generando reporte para fecha: {self.fecha}")
        
        try:
            # Obtener datos de MongoDB
            print("Obteniendo datos de MongoDB...")
            df = self.mongo_client.get_casos_dataframe(self.fecha)
            
            if df.empty:
                print(f"No se encontraron datos para la fecha {self.fecha}")
                return None
            
            print(f"Obtenidos {len(df)} casos de la base de datos")
            
            # Analizar llamadas
            print("Analizando estado de llamadas...")
            numeros_caso = df['caso'].astype(str).tolist()
            call_results = self.call_analyzer.analizar_multiples_llamadas(numeros_caso)
            
            # Actualizar DataFrame con resultados
            df_updated = self.excel_generator.update_call_results(df, call_results)
            
            # Generar reporte Excel
            print("Generando archivo Excel...")
            excel_path = self.excel_generator.generate_excel_report(
                df_updated, 
                filename=f"{self.fecha}.xlsx"
            )
            
            # Enviar por email si se solicita
            if send_email and excel_path:
                print("Enviando reporte por email...")
                success = self.email_sender.send_report(
                    subject=f"Reporte {self.fecha}",
                    message="Reporte Voicebot",
                    file_paths=[excel_path]
                )
                
                if success:
                    print("Reporte enviado por email exitosamente")
                else:
                    print("Error enviando reporte por email")
            
            return excel_path
            
        except Exception as e:
            print(f"Error generando reporte: {e}")
            return None
    
    def run_full_process(self, copy_logs: bool = True, send_email: bool = True) -> bool:
        """
        Ejecuta el proceso completo de generación de reportes
        
        Args:
            copy_logs: Si copiar logs de Docker
            send_email: Si enviar reporte por email
            
        Returns:
            True si el proceso fue exitoso
        """
        print("=== Iniciando proceso completo de reportes ===")
        
        try:
            # Inicializar componentes
            if not self.initialize_components():
                return False
            
            # Copiar logs si se solicita
            if copy_logs:
                if not self.copy_asterisk_logs():
                    print("Advertencia: No se pudieron copiar los logs, continuando con logs existentes")
            
            # Procesar logs
            if not self.process_logs():
                print("Error: No se pudieron procesar los logs")
                return False
            
            # Generar reporte
            excel_path = self.generate_report(send_email=send_email)
            
            if excel_path:
                print(f"=== Proceso completado exitosamente ===")
                print(f"Reporte generado: {excel_path}")
                return True
            else:
                print("=== Proceso falló ===")
                return False
                
        except Exception as e:
            print(f"Error en proceso completo: {e}")
            return False
        
        finally:
            # Limpiar recursos
            if self.mongo_client:
                self.mongo_client.close_connection()
    
    def generate_statistics_report(self) -> dict:
        """Genera un reporte de estadísticas"""
        print("Generando reporte de estadísticas...")
        
        try:
            # Obtener datos
            df = self.mongo_client.get_casos_dataframe(self.fecha)
            if df.empty:
                return {}
            
            # Analizar llamadas
            numeros_caso = df['caso'].astype(str).tolist()
            reporte = self.call_analyzer.generar_reporte_estado(numeros_caso)
            
            return reporte
            
        except Exception as e:
            print(f"Error generando estadísticas: {e}")
            return {}


def main():
    """Función principal"""
    parser = argparse.ArgumentParser(description='Sistema de Reportes de Voicebot')
    parser.add_argument('--fecha', type=str, help='Fecha del reporte (YYYY-MM-DD)')
    parser.add_argument('--no-copy-logs', action='store_true', help='No copiar logs de Docker')
    parser.add_argument('--no-email', action='store_true', help='No enviar reporte por email')
    parser.add_argument('--stats-only', action='store_true', help='Solo generar estadísticas')
    parser.add_argument('--test-email', action='store_true', help='Enviar email de prueba')
    
    args = parser.parse_args()
    
    # Validar fecha si se proporciona
    if args.fecha:
        try:
            datetime.strptime(args.fecha, '%Y-%m-%d')
        except ValueError:
            print("Error: Formato de fecha inválido. Use YYYY-MM-DD")
            return 1
    
    # Crear instancia del reporter
    reporter = VoicebotReporter(fecha=args.fecha)
    
    # Ejecutar según argumentos
    if args.test_email:
        print("Enviando email de prueba...")
        if reporter.initialize_components():
            success = reporter.email_sender.send_test_email()
            return 0 if success else 1
        return 1
    
    elif args.stats_only:
        if reporter.initialize_components():
            stats = reporter.generate_statistics_report()
            print("\n=== Estadísticas ===")
            for key, value in stats.get('estadisticas', {}).items():
                print(f"{key}: {value}")
            return 0
        return 1
    
    else:
        # Proceso completo
        copy_logs = not args.no_copy_logs
        send_email = not args.no_email
        
        success = reporter.run_full_process(copy_logs=copy_logs, send_email=send_email)
        return 0 if success else 1


if __name__ == "__main__":
    exit(main())
