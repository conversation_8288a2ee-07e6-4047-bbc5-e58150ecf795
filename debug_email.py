#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug del sistema de email
"""
import sys
import os

sys.path.append('.')

def test_email_config():
    """Prueba la configuración de email"""
    print("=== DIAGNÓSTICO DEL SISTEMA DE EMAIL ===\n")
    
    # 1. Verificar imports
    print("1. Verificando imports...")
    try:
        from src.reports.email_sender import Mail
        print("   ✅ Import Mail: OK")
    except Exception as e:
        print(f"   ❌ Error import Mail: {e}")
        return False
    
    # 2. Verificar configuración
    print("2. Verificando configuración...")
    try:
        from config.settings import EMAIL_CONFIG
        print(f"   ✅ Destinatarios configurados: {EMAIL_CONFIG['recipients']}")
        print(f"   ✅ Asunto template: {EMAIL_CONFIG['subject_template']}")
    except Exception as e:
        print(f"   ❌ Error configuración: {e}")
        return False
    
    # 3. Verificar variables de entorno
    print("3. Verificando variables de entorno...")
    email_user = os.getenv('EMAIL_USERNAME')
    email_pass = os.getenv('EMAIL_PASSWORD')
    
    if email_user:
        print(f"   ✅ EMAIL_USERNAME: {email_user}")
    else:
        print("   ⚠️  EMAIL_USERNAME no configurado")
    
    if email_pass:
        print("   ✅ EMAIL_PASSWORD: [CONFIGURADO]")
    else:
        print("   ⚠️  EMAIL_PASSWORD no configurado")
    
    # 4. Probar creación de instancia Mail
    print("4. Probando instancia Mail...")
    try:
        mail = Mail()
        print("   ✅ Instancia Mail creada")
        
        # Verificar URL del endpoint
        print(f"   📡 URL endpoint: {mail._Mail__url}")
        print(f"   📧 Tipo de mail: {mail._Mail__type}")
        print(f"   📋 Contrato: {mail._Mail__contract}")
        
    except Exception as e:
        print(f"   ❌ Error creando Mail: {e}")
        return False
    
    # 5. Probar envío de email de prueba
    print("5. Probando envío de email...")
    try:
        response = mail.send(
            to="<EMAIL>",
            subject="Prueba Sistema Voicebot - Debug",
            message="Este es un email de prueba para verificar la configuración.",
            assert_sent=False,  # No verificar respuesta por ahora
            ignore_errors=True  # Ignorar errores para debug
        )
        
        if response:
            print(f"   ✅ Respuesta del servidor: {response.status_code}")
            print(f"   📄 Contenido: {response.text[:200]}...")
        else:
            print("   ❌ No se obtuvo respuesta")
            
    except Exception as e:
        print(f"   ❌ Error enviando email: {e}")
        return False
    
    return True

def test_endpoint_connectivity():
    """Prueba la conectividad al endpoint de Mosaico"""
    print("\n=== PRUEBA DE CONECTIVIDAD ===")
    
    try:
        import requests
        
        url = "https://mosaico.arus.com.co:3000/mailer/withAttachments"
        print(f"Probando conectividad a: {url}")
        
        # Hacer una petición simple para verificar conectividad
        response = requests.get("https://mosaico.arus.com.co:3000", 
                              timeout=10, verify=False)
        
        print(f"   ✅ Conectividad: OK (Status: {response.status_code})")
        return True
        
    except requests.exceptions.SSLError as e:
        print(f"   ⚠️  Error SSL: {e}")
        print("   💡 Esto es normal con verify=False")
        return True
    except requests.exceptions.ConnectionError as e:
        print(f"   ❌ Error de conexión: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def show_email_setup_guide():
    """Muestra guía para configurar email"""
    print("\n=== GUÍA DE CONFIGURACIÓN ===")
    
    print("Para que el email funcione, necesitas:")
    print()
    print("1. 📧 CONFIGURAR DESTINATARIOS:")
    print("   Editar config/settings.py:")
    print("   EMAIL_CONFIG['recipients'] = ['<EMAIL>']")
    print()
    print("2. 🔗 VERIFICAR ENDPOINT MOSAICO:")
    print("   URL: https://mosaico.arus.com.co:3000/mailer/withAttachments")
    print("   Tipo: divulgacion_mosaico")
    print("   Contrato: 5d682412e0a6e100062cc5cc")
    print()
    print("3. 🌐 VERIFICAR CONECTIVIDAD:")
    print("   - Red corporativa de ARUS")
    print("   - VPN si trabajas remoto")
    print("   - Firewall/proxy configurado")
    print()
    print("4. 📋 VERIFICAR DATOS:")
    print("   - Destinatario válido")
    print("   - Asunto no vacío")
    print("   - Mensaje no vacío")

def main():
    print("=" * 60)
    print("DEBUG DEL SISTEMA DE EMAIL")
    print("=" * 60)
    
    success1 = test_email_config()
    success2 = test_endpoint_connectivity()
    
    if not success1 or not success2:
        show_email_setup_guide()
    
    print(f"\n" + "=" * 60)
    if success1 and success2:
        print("✅ CONFIGURACIÓN BÁSICA OK")
        print("   Si aún no llega email, revisar:")
        print("   - Destinatario correcto")
        print("   - Conectividad a mosaico.arus.com.co")
        print("   - Logs del servidor Mosaico")
    else:
        print("❌ PROBLEMAS DE CONFIGURACIÓN DETECTADOS")
    print("=" * 60)

if __name__ == "__main__":
    main()
