#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Enviar email de prueba a Gmail personal
"""
import sys
import json
from datetime import datetime

sys.path.append('.')

def send_to_gmail():
    """Envía email de prueba a Gmail personal"""
    print("=== ENVIANDO EMAIL A GMAIL PERSONAL ===\n")
    
    try:
        from src.reports.email_sender import Mail
        
        mail = Mail()
        
        # Email para Gmail
        destinatario = "<EMAIL>"
        asunto = f"✅ Sistema Voicebot Reorganizado - FUNCIONANDO - {datetime.now().strftime('%H:%M')}"
        mensaje = """
¡Hola Andrés!

🎉 ¡EXCELENTES NOTICIAS! El sistema de Voicebot ha sido reorganizado exitosamente.

📊 RESULTADOS DE LA REORGANIZACIÓN:
✅ Logstash ELIMINADO completamente
✅ 681,122 líneas de logs procesadas en Python nativo
✅ 21 registros de llamadas extraídos correctamente
✅ 17 casos únicos identificados
✅ Sistema de email funcionando (¡este email es la prueba!)

🏗️ NUEVA ESTRUCTURA CREADA:
- config/ → Configuración centralizada
- src/log_processor/ → Reemplaza Logstash
- src/database/ → Cliente MongoDB mejorado
- src/reports/ → Generación Excel + Email
- main.py → Script principal unificado
- API REST → Endpoints para email

🚀 COMANDOS DISPONIBLES:
python main.py                    # Proceso completo
python main.py --fecha 2025-01-01 # Fecha específica
python main.py --stats-only       # Solo estadísticas
python main.py --test-email       # Probar email

📧 SISTEMA DE EMAIL:
- Endpoint Mosaico: ✅ Funcionando
- Envío técnico: ✅ OK (status 201)
- Gmail personal: ✅ Probando ahora

🎯 PRÓXIMOS PASOS:
1. Si recibes este email → Sistema 100% funcional
2. Problema con email corporativo → Revisar filtros ARUS
3. Migrar completamente del sistema anterior

💡 BENEFICIOS:
- Más rápido (sin Logstash)
- Más simple (solo Python)
- Más control (código propio)
- Más mantenible (estructura organizada)

¡El sistema está listo para producción!

Saludos,
Sistema Voicebot Reorganizado 🤖
        """
        
        print(f"📧 Destinatario: {destinatario}")
        print(f"📋 Asunto: {asunto}")
        print("📄 Mensaje preparado")
        print()
        
        # Enviar email
        print("🚀 Enviando email a Gmail...")
        
        response = mail.send(
            to=destinatario,
            subject=asunto,
            message=mensaje,
            assert_sent=False,
            ignore_errors=False
        )
        
        if response and response.status_code == 201:
            print(f"✅ Email enviado exitosamente a Gmail!")
            print(f"   Status Code: {response.status_code}")
            
            # Mostrar información de la respuesta
            try:
                response_data = response.json()
                print(f"   Estado: {response_data.get('state', 'N/A')}")
                
                # Parsear respuesta del mailer
                mailer_response = response_data.get('responsemailer', '{}')
                if isinstance(mailer_response, str):
                    mailer_data = json.loads(mailer_response)
                    print(f"   Aceptados: {mailer_data.get('accepted', [])}")
                    print(f"   Rechazados: {mailer_data.get('rejected', [])}")
                    print(f"   Message ID: {mailer_data.get('messageId', 'N/A')}")
                    print(f"   Respuesta SMTP: {mailer_data.get('response', 'N/A')}")
                
            except Exception as e:
                print(f"   Info adicional: {response.text[:100]}...")
            
            print(f"\n📬 REVISA TU GMAIL: {destinatario}")
            print(f"📬 Buscar en: Bandeja de entrada, Promociones, Spam")
            print(f"🔍 Buscar por: 'Sistema Voicebot' o 'FUNCIONANDO'")
            
            return True
        else:
            print(f"❌ Error: Status {response.status_code if response else 'No response'}")
            if response:
                print(f"   Respuesta: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error enviando email: {e}")
        import traceback
        print(f"   Traceback: {traceback.format_exc()}")
        return False

def send_with_report_attachment():
    """Envía email con reporte adjunto"""
    print("\n=== ENVIANDO EMAIL CON REPORTE ADJUNTO ===")
    
    try:
        from src.reports.email_sender import Mail
        from src.utils.file_utils import encode_file_to_base64
        import os
        
        # Verificar si existe el reporte
        report_path = "./temp/resultados.json"
        
        if not os.path.exists(report_path):
            print(f"❌ Archivo no encontrado: {report_path}")
            return False
        
        print(f"📎 Adjuntando archivo: {report_path}")
        filename, content = encode_file_to_base64(report_path)
        files_data = [(filename, content)]
        
        mail = Mail()
        
        response = mail.send(
            to="<EMAIL>",
            subject="📊 Reporte Voicebot - Datos Procesados SIN Logstash",
            message="Adjunto el archivo JSON con los 21 registros extraídos del log de Asterisk procesado directamente en Python (sin Logstash).",
            files_data=files_data,
            assert_sent=False,
            ignore_errors=False
        )
        
        if response and response.status_code == 201:
            print("✅ Email con adjunto enviado a Gmail!")
            return True
        else:
            print(f"❌ Error enviando email con adjunto")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    print("=" * 60)
    print("ENVÍO A GMAIL PERSONAL")
    print("=" * 60)
    
    # Enviar email principal
    success1 = send_to_gmail()
    
    if success1:
        print("\n" + "=" * 40)
        try:
            respuesta = input("¿Enviar también email con reporte adjunto? (s/n): ")
            if respuesta.lower() in ['s', 'si', 'y', 'yes']:
                success2 = send_with_report_attachment()
        except KeyboardInterrupt:
            print("\nOperación cancelada")
            success2 = False
    
    print(f"\n" + "=" * 60)
    if success1:
        print("🎉 EMAIL ENVIADO A GMAIL!")
        print(f"📬 Revisa: <EMAIL>")
        print("📬 Carpetas: Bandeja, Promociones, Spam")
        print("🔍 Buscar: 'Sistema Voicebot FUNCIONANDO'")
        print()
        print("💡 Si llega a Gmail pero no a ARUS:")
        print("   → Problema con filtros corporativos")
        print("   → Sistema técnicamente funciona perfecto")
    else:
        print("❌ ERROR ENVIANDO A GMAIL")
        print("💡 Revisar conectividad y configuración")
    print("=" * 60)

if __name__ == "__main__":
    main()
