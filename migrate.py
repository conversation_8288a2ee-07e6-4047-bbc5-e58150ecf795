#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de migración para facilitar la transición del código existente
"""
import os
import shutil
from datetime import datetime


def backup_old_files():
    """Hace backup de los archivos existentes"""
    backup_dir = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    files_to_backup = [
        'py.py',
        'log_processor.py',
        'logstash'
    ]
    
    print(f"Creando backup en: {backup_dir}")
    os.makedirs(backup_dir, exist_ok=True)
    
    for file in files_to_backup:
        if os.path.exists(file):
            shutil.copy2(file, backup_dir)
            print(f"Backup creado: {file} -> {backup_dir}/{file}")
    
    return backup_dir


def show_migration_guide():
    """Muestra guía de migración"""
    print("\n" + "="*60)
    print("GUÍA DE MIGRACIÓN")
    print("="*60)
    
    print("\n1. ARCHIVOS ORGANIZADOS:")
    print("   ✅ Estructura modular creada")
    print("   ✅ Configuración centralizada")
    print("   ✅ Código limpio y documentado")
    
    print("\n2. REEMPLAZO DE LOGSTASH:")
    print("   ❌ Logstash eliminado")
    print("   ✅ Procesamiento nativo en Python")
    print("   ✅ Mejor rendimiento y control")
    
    print("\n3. NUEVAS FUNCIONALIDADES:")
    print("   ✅ Script principal unificado (main.py)")
    print("   ✅ Argumentos de línea de comandos")
    print("   ✅ Mejor manejo de errores")
    print("   ✅ Estadísticas detalladas")
    
    print("\n4. COMPATIBILIDAD:")
    print("   ✅ email_class.py mantiene interfaz existente")
    print("   ✅ analizador.py mantiene interfaz existente")
    print("   ✅ Funciones de log_processor disponibles")
    
    print("\n5. PRÓXIMOS PASOS:")
    print("   1. Instalar dependencias: pip install -r requirements.txt")
    print("   2. Configurar .env con credenciales de email")
    print("   3. Probar: python main.py --test-email")
    print("   4. Ejecutar: python main.py")
    
    print("\n6. COMANDOS ÚTILES:")
    print("   python main.py                    # Proceso completo")
    print("   python main.py --fecha 2025-01-01 # Fecha específica")
    print("   python main.py --stats-only       # Solo estadísticas")
    print("   python main.py --no-email         # Sin enviar email")
    print("   python main.py --help             # Ayuda completa")


def check_dependencies():
    """Verifica dependencias"""
    print("\n" + "="*60)
    print("VERIFICACIÓN DE DEPENDENCIAS")
    print("="*60)
    
    required_modules = [
        'pandas',
        'pymongo',
        'chardet',
        'openpyxl'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"   ✅ {module}")
        except ImportError:
            print(f"   ❌ {module} - FALTANTE")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n⚠️  INSTALAR DEPENDENCIAS FALTANTES:")
        print(f"   pip install {' '.join(missing_modules)}")
        print(f"   O ejecutar: pip install -r requirements.txt")
    else:
        print(f"\n✅ TODAS LAS DEPENDENCIAS ESTÁN INSTALADAS")


def main():
    """Función principal de migración"""
    print("SISTEMA DE REPORTES DE VOICEBOT - MIGRACIÓN")
    print("="*60)
    
    # Hacer backup
    backup_dir = backup_old_files()
    
    # Verificar dependencias
    check_dependencies()
    
    # Mostrar guía
    show_migration_guide()
    
    print("\n" + "="*60)
    print("MIGRACIÓN COMPLETADA")
    print("="*60)
    print(f"Backup creado en: {backup_dir}")
    print("El sistema está listo para usar!")
    print("\nPróximo paso: python main.py --test-email")


if __name__ == "__main__":
    main()
