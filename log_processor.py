
# -*- coding: utf-8 -*-
import re
import chardet


def filtrar_logs_y_extraer_variables(log_file_path, numero_caso):
    # Expresión regular para capturar los campos
    pattern = r'Executing .* AGI\(\".*\", \"reporte\.py, \"(' + re.escape(numero_caso) + r')\", \"([^\"]+)\", \"([0-9]+)\"'

    try:
        with open(log_file_path, 'rb') as archivo:
            contenido = archivo.read()
            resultado = chardet.detect(contenido)
            codificacion = resultado['encoding']
            print(codificacion)

        with open(log_file_path, 'r', encoding='utf-8', errors='ignore') as archivo:
            file = archivo.read()





        #with open(log_file_path, 'r') as file:
            for line in file:
                match = re.search(pattern, line)
                if match:
                    numero = match.group(1)
                    accion = match.group(2)
                    calificacion = match.group(3)
                    return (numero, accion, calificacion)  # Retornar como tupla
    except FileNotFoundError:
        print(f"Error: El archivo {log_file_path} no se encontró.")
    except Exception as e:
        print(f"Ocurrió un error: {e}")

    return None  # Retornar None si no hay coincidencias
def buscar_llamada(log_file_path, numero_caso):
    # Expresión regular para capturar los campos
    pattern = r'Executing .* AGI\(\".*\", \"reporte\.py, \"(' + re.escape(numero_caso) + r')\", \"([^\"]+)\"'

    try:
        #with open(log_file_path, 'r') as file:
        #with open(log_file_path, 'r') as file:
        with open(log_file_path, 'r', encoding='utf-8', errors='ignore') as archivo:
            file = archivo.read()
            for line in file:
                match = re.search(pattern, line)
                if match:
                    numero = match.group(1)
                    accion = match.group(2)
                    print("lo encontro",numero)
                    return (numero, accion)  # Retornar como tupla
    except FileNotFoundError:
        print(f"Error: El archivo {log_file_path} no se encontró.")
    except Exception as e:
        print(f"Ocurrió un error: {e}")

    return None  # Retornar None si no hay coincidencias




print(buscar_llamada("/tmp/full_log", "6723221"))
