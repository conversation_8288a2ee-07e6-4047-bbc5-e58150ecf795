#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sistema Voicebot Optimizado - Versión Final
Automatización completa sin Logstash
"""
import pandas as pd
import json
import pymongo
import base64
import os
from datetime import datetime
import sys

# Agregar el directorio actual al path
sys.path.append('.')

def get_mongodb_data(fecha):
    """Obtiene datos de MongoDB"""
    print(f"🗄️  Conectando a MongoDB para fecha: {fecha}")
    
    try:
        client = pymongo.MongoClient("********************************************/")
        db = client['mydatabase']
        collection = db[fecha]
        
        data = list(collection.find({}))
        
        if not data:
            print(f"❌ No se encontraron datos para la fecha {fecha}")
            
            # Mostrar fechas disponibles
            print("📅 Fechas disponibles en MongoDB:")
            collections = db.list_collection_names()
            date_collections = [col for col in collections if len(col) == 10 and col.count('-') == 2]
            for col in sorted(date_collections)[-10:]:  # Últimas 10 fechas
                count = db[col].count_documents({})
                print(f"   {col}: {count} registros")
            
            return None, client
        
        # Convertir ObjectId a string
        for record in data:
            record["_id"] = str(record["_id"])
        
        df = pd.json_normalize(data)
        print(f"✅ Obtenidos {len(df)} registros de MongoDB")
        
        return df, client
        
    except Exception as e:
        print(f"❌ Error conectando a MongoDB: {e}")
        return None, None

def process_logs_to_json():
    """Procesa logs usando archivo local (reemplaza Logstash)"""
    print("📊 Procesando logs (reemplazando Logstash)...")
    
    try:
        from src.log_processor.asterisk_parser import AsteriskLogParser
        
        # Usar archivo local
        log_path = "../full_log"  # Archivo en directorio padre
        if not os.path.exists(log_path):
            print(f"❌ Archivo de logs no encontrado: {log_path}")
            return False
        
        parser = AsteriskLogParser(log_path)
        
        # Crear directorio temp si no existe
        os.makedirs("./temp", exist_ok=True)
        
        success = parser.guardar_resultados_json("./temp/resultados.json")
        
        if success:
            print("✅ Logs procesados y JSON generado")
            return True
        else:
            print("❌ Error procesando logs")
            return False
            
    except Exception as e:
        print(f"❌ Error en procesamiento: {e}")
        return False

def process_dataframe_with_logs(df):
    """Procesa DataFrame con logs"""
    print("🔄 Procesando DataFrame con logs...")
    
    try:
        # Verificar si la columna 'caso' existe
        if 'caso' not in df.columns:
            print(f"❌ Columna 'caso' no encontrada. Columnas disponibles: {list(df.columns)}")
            return None
        
        # Inicializar columnas
        df['calificacion'] = 'FALSO'
        df['realizada'] = 'FALSO'
        df['finalizo'] = 'FALSO'
        
        # Cargar logs desde JSON
        ruta_archivo_logs = "./temp/resultados.json"
        
        if not os.path.exists(ruta_archivo_logs):
            print(f"❌ Archivo de logs no encontrado: {ruta_archivo_logs}")
            return None
        
        with open(ruta_archivo_logs, "r", encoding='utf-8') as archivo:
            logs = [json.loads(line) for line in archivo if line.strip()]
        
        print(f"📋 Cargados {len(logs)} registros de logs")
        
        # Crear analizador
        from src.log_processor.call_analyzer import AnalizadorLlamadas
        analizador = AnalizadorLlamadas("../full_log")
        
        # Procesar cada fila
        casos_procesados = 0
        casos_encontrados = 0
        
        for index, row in df.iterrows():
            numero_caso = str(row["caso"])
            
            # Analizar llamada
            resultado = analizador.analizar_llamada(numero_caso)
            print(f"Caso {numero_caso}: {resultado['estado']}")
            
            if resultado['estado'] == "Completado":
                df.loc[index, "realizada"] = "VERDADERO"
            
            # Buscar en logs procesados
            for log in logs:
                if log.get('numero_caso') == str(numero_caso):
                    calificacion = log.get('calificacion')
                    
                    if calificacion and calificacion.strip():
                        try:
                            cal_int = int(calificacion)
                            if cal_int > 5:
                                cal_int = 5
                            df.loc[index, "calificacion"] = str(cal_int)
                            df.loc[index, "realizada"] = "VERDADERO"
                            df.loc[index, "finalizo"] = "VERDADERO"
                            casos_encontrados += 1
                            print(f"  ✅ Calificación actualizada a: {cal_int}")
                        except ValueError:
                            print(f"  ⚠️  Calificación inválida: {calificacion}")
                    else:
                        df.loc[index, "realizada"] = "VERDADERO"
                        print(f"  📞 Llamada encontrada sin calificación")
                    break
            else:
                print(f"  ❌ No se encontraron resultados")
            
            casos_procesados += 1
            
            # Mostrar progreso cada 10 casos
            if casos_procesados % 10 == 0:
                print(f"   Procesados {casos_procesados}/{len(df)} casos...")
        
        print(f"✅ Procesados {casos_procesados} casos, {casos_encontrados} con calificación")
        return df
        
    except Exception as e:
        print(f"❌ Error procesando DataFrame: {e}")
        import traceback
        print(traceback.format_exc())
        return None

def create_excel_report(df, fecha):
    """Crea reporte Excel"""
    print("📊 Creando reporte Excel...")
    
    try:
        # Orden de columnas
        column_order = [
            '_id', 'telefono', 'descripcion', 'caso_db', 'nombre', 
            'apellido', 'realizada', 'calificacion', 'finalizo', 
            'reabrir', 'fecha', 'caso', 'asignado', 'grupo'
        ]
        
        # Verificar columnas existentes
        existing_columns = [col for col in column_order if col in df.columns]
        missing_columns = [col for col in column_order if col not in df.columns]
        
        print(f"📋 Columnas existentes: {len(existing_columns)}")
        if missing_columns:
            print(f"⚠️  Columnas faltantes: {missing_columns}")
            for col in missing_columns:
                df[col] = ''
        
        # Reordenar columnas
        df = df[column_order]
        
        # Eliminar duplicados por 'caso'
        df_sin_duplicados = df.drop_duplicates(subset=['caso'])
        
        # Normalizar valores booleanos
        df_sin_duplicados = df_sin_duplicados.applymap(
            lambda x: x.replace('true', 'VERDADERO') if isinstance(x, str) else x
        )
        df_sin_duplicados = df_sin_duplicados.applymap(
            lambda x: x.replace('false', 'FALSO') if isinstance(x, str) else x
        )
        
        # Crear directorio
        output_dir = "./reports/"
        os.makedirs(output_dir, exist_ok=True)
        
        # Guardar Excel
        excel_path = os.path.join(output_dir, f"{fecha}.xlsx")
        df_sin_duplicados.to_excel(excel_path, index=False)
        
        print(f"✅ Archivo Excel creado: {excel_path}")
        print(f"   Registros originales: {len(df)}")
        print(f"   Registros finales (sin duplicados): {len(df_sin_duplicados)}")
        
        return excel_path
        
    except Exception as e:
        print(f"❌ Error creando Excel: {e}")
        import traceback
        print(traceback.format_exc())
        return None

def send_email_report(excel_path, fecha):
    """Envía email"""
    print("📧 Enviando reporte por email...")
    
    try:
        def encode_file_to_base64(file_path):
            with open(file_path, "rb") as file:
                encoded_content = base64.b64encode(file.read()).decode("utf-8")
            return (file_path.split("/")[-1].split("\\")[-1], encoded_content)
        
        file_paths = [excel_path]
        files_data = [encode_file_to_base64(path) for path in file_paths]
        
        from src.reports.email_sender import Mail
        
        mi_clase = Mail()
        response = mi_clase.send(
            to="<EMAIL>",
            subject=f"Reporte Voicebot Optimizado {fecha}",
            message=f"Reporte automatizado del sistema Voicebot optimizado para la fecha {fecha}",
            files_data=files_data
        )
        
        if response and response.status_code == 201:
            print("✅ Email enviado exitosamente")
            return True
        else:
            print(f"❌ Error enviando email: {response.status_code if response else 'No response'}")
            return False
        
    except Exception as e:
        print(f"❌ Error enviando email: {e}")
        return False

def main():
    """Función principal"""
    print("=" * 60)
    print("🤖 SISTEMA VOICEBOT OPTIMIZADO")
    print("=" * 60)
    
    # Usar fecha específica que sabemos que existe
    fecha = "2025-06-24"
    print(f"📅 Fecha: {fecha}")
    
    client = None
    
    try:
        # Paso 1: Obtener datos de MongoDB
        df, client = get_mongodb_data(fecha)
        if df is None:
            return False
        
        # Paso 2: Procesar logs
        if not process_logs_to_json():
            return False
        
        # Paso 3: Procesar DataFrame con logs
        df_processed = process_dataframe_with_logs(df)
        if df_processed is None:
            return False
        
        # Paso 4: Crear Excel
        excel_path = create_excel_report(df_processed, fecha)
        if not excel_path:
            return False
        
        # Paso 5: Enviar email
        if send_email_report(excel_path, fecha):
            print(f"\n🎉 AUTOMATIZACIÓN COMPLETADA!")
            print(f"📧 Revisa <EMAIL>")
            print(f"📊 Excel: {excel_path}")
            return True
        else:
            return False
            
    except Exception as e:
        print(f"❌ Error general: {e}")
        import traceback
        print(traceback.format_exc())
        return False
    
    finally:
        if client:
            client.close()
            print("🔌 Conexión MongoDB cerrada")

if __name__ == "__main__":
    main()
