# API REST - Sistema de Reportes Voicebot

Documentación de las APIs REST para el sistema de reportes de Voicebot. Se proporcionan dos implementaciones: **FastAPI** (recomendada) y **Flask**.

## 🚀 Inicio Rápido

### Opción 1: FastAPI (Recomendada)
```bash
# Instalar dependencias
pip install fastapi uvicorn pydantic[email]

# Ejecutar servidor
python api_fastapi.py

# Acceder a documentación interactiva
# http://localhost:8000/docs
```

### Opción 2: Flask
```bash
# Instalar dependencias
pip install flask flask-cors

# Ejecutar servidor
python api_flask.py

# API disponible en http://localhost:5000
```

## 📋 Endpoints Disponibles

### 1. **GET /** - Información de la API
```bash
curl http://localhost:8000/
```

**Respuesta:**
```json
{
  "message": "Sistema de Reportes Voicebot API",
  "version": "1.0.0",
  "endpoints": {
    "docs": "/docs",
    "health": "/health",
    "send_email": "/send-email",
    "generate_report": "/generate-report",
    "get_statistics": "/statistics",
    "download_report": "/download-report/{fecha}"
  }
}
```

### 2. **GET /health** - Health Check
```bash
curl http://localhost:8000/health
```

**Respuesta:**
```json
{
  "status": "healthy",
  "timestamp": "2025-01-01T12:00:00",
  "components": {
    "mongodb": "ok",
    "email": "ok",
    "log_processor": "ok"
  }
}
```

### 3. **POST /send-email** - Enviar Email
Envía un email con o sin reporte adjunto.

```bash
curl -X POST http://localhost:8000/send-email \
  -H "Content-Type: application/json" \
  -d '{
    "to": "<EMAIL>",
    "subject": "Reporte Voicebot",
    "message": "Adjunto el reporte solicitado",
    "fecha": "2025-01-01",
    "include_report": true
  }'
```

**Parámetros:**
- `to` (string, requerido): Email del destinatario
- `subject` (string, requerido): Asunto del email
- `message` (string, requerido): Mensaje del email
- `fecha` (string, opcional): Fecha del reporte (YYYY-MM-DD)
- `include_report` (boolean, opcional): Incluir reporte Excel (default: true)

**Respuesta:**
```json
{
  "success": true,
  "message": "Email programado para envío",
  "email_sent_to": "<EMAIL>"
}
```

### 4. **POST /generate-report** - Generar Reporte
Genera un reporte completo con procesamiento de logs y análisis.

```bash
curl -X POST http://localhost:8000/generate-report \
  -H "Content-Type: application/json" \
  -d '{
    "fecha": "2025-01-01",
    "send_email": true,
    "copy_logs": true
  }'
```

**Parámetros:**
- `fecha` (string, opcional): Fecha del reporte (default: hoy)
- `send_email` (boolean, opcional): Enviar por email (default: true)
- `copy_logs` (boolean, opcional): Copiar logs de Docker (default: true)

**Respuesta:**
```json
{
  "success": true,
  "message": "Reporte generado exitosamente",
  "report_path": "/home/<USER>/reporte/2025-01-01.xlsx",
  "fecha": "2025-01-01",
  "statistics": {
    "total_casos": 150,
    "completados": 120,
    "iniciados": 20,
    "no_encontrados": 10
  }
}
```

### 5. **GET /statistics** - Obtener Estadísticas
Obtiene estadísticas de llamadas para una fecha específica.

```bash
curl "http://localhost:8000/statistics?fecha=2025-01-01"
```

**Parámetros de Query:**
- `fecha` (string, opcional): Fecha en formato YYYY-MM-DD

**Respuesta:**
```json
{
  "fecha": "2025-01-01",
  "estadisticas": {
    "total_casos": 150,
    "completados": 120,
    "iniciados": 20,
    "no_encontrados": 10,
    "con_calificacion": 115,
    "calificaciones_altas": 80
  },
  "timestamp": "2025-01-01T12:00:00"
}
```

### 6. **GET /download-report/{fecha}** - Descargar Reporte
Descarga el archivo Excel del reporte.

```bash
curl -O http://localhost:8000/download-report/2025-01-01
```

**Parámetros:**
- `fecha` (string, requerido): Fecha del reporte en formato YYYY-MM-DD

**Respuesta:** Archivo Excel descargado

### 7. **POST /test-email** - Probar Email (Solo Flask)
Envía un email de prueba para verificar configuración.

```bash
curl -X POST http://localhost:5000/test-email \
  -H "Content-Type: application/json" \
  -d '{
    "to": "<EMAIL>"
  }'
```

## 🔧 Configuración

### Variables de Entorno
Crear archivo `.env`:
```env
# Email
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=tu_contraseña_de_aplicacion
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587

# MongoDB
MONGODB_HOST=***********
MONGODB_PORT=27017
MONGODB_USERNAME=admin
MONGODB_PASSWORD=V0iC3boatT
```

### Configuración de Producción

#### FastAPI con Gunicorn
```bash
pip install gunicorn
gunicorn api_fastapi:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

#### Flask con Gunicorn
```bash
pip install gunicorn
gunicorn api_flask:app -w 4 --bind 0.0.0.0:5000
```

## 📊 Ejemplos de Uso

### Generar y Enviar Reporte Diario
```python
import requests

# Generar reporte para hoy
response = requests.post('http://localhost:8000/generate-report', json={
    "send_email": True,
    "copy_logs": True
})

if response.status_code == 200:
    data = response.json()
    print(f"Reporte generado: {data['report_path']}")
    print(f"Estadísticas: {data['statistics']}")
```

### Enviar Email Personalizado
```python
import requests

# Enviar email con reporte específico
response = requests.post('http://localhost:8000/send-email', json={
    "to": "<EMAIL>",
    "subject": "Reporte Semanal Voicebot",
    "message": "Adjunto el reporte semanal de llamadas del voicebot.",
    "fecha": "2025-01-01",
    "include_report": True
})

if response.status_code == 200:
    print("Email enviado exitosamente")
```

### Obtener Estadísticas
```python
import requests

# Obtener estadísticas del día
response = requests.get('http://localhost:8000/statistics')
data = response.json()

print(f"Total casos: {data['estadisticas']['total_casos']}")
print(f"Completados: {data['estadisticas']['completados']}")
```

## 🔒 Seguridad

### Recomendaciones para Producción
1. **HTTPS**: Usar certificados SSL/TLS
2. **Autenticación**: Implementar API keys o JWT
3. **Rate Limiting**: Limitar requests por IP
4. **Firewall**: Restringir acceso a IPs autorizadas
5. **Logs**: Monitorear accesos y errores

### Ejemplo con API Key (FastAPI)
```python
from fastapi import Header, HTTPException

async def verify_api_key(x_api_key: str = Header()):
    if x_api_key != "tu-api-key-secreta":
        raise HTTPException(status_code=401, detail="API Key inválida")
    return x_api_key

# Agregar a endpoints protegidos
@app.post("/generate-report", dependencies=[Depends(verify_api_key)])
async def generate_report(report_request: ReportRequest):
    # ... código del endpoint
```

## 🐳 Docker

### Dockerfile para FastAPI
```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["uvicorn", "api_fastapi:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Docker Compose
```yaml
version: '3.8'
services:
  voicebot-api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - EMAIL_USERNAME=${EMAIL_USERNAME}
      - EMAIL_PASSWORD=${EMAIL_PASSWORD}
    volumes:
      - ./reports:/home/<USER>/reporte
```

## 🔍 Monitoreo y Logs

### Logs de la API
- FastAPI: Logs automáticos con uvicorn
- Flask: Configurar logging personalizado

### Métricas Recomendadas
- Requests por minuto
- Tiempo de respuesta
- Errores 4xx/5xx
- Reportes generados exitosamente
- Emails enviados

## 🆘 Solución de Problemas

### Error 503 - Service Unavailable
- Verificar conexión a MongoDB
- Verificar credenciales de email
- Revisar logs del contenedor Docker

### Error 500 - Internal Server Error
- Verificar permisos de archivos
- Revisar configuración en `.env`
- Verificar dependencias instaladas

### Timeout en Generación de Reportes
- Aumentar timeout del servidor web
- Optimizar consultas a MongoDB
- Usar procesamiento asíncrono
