#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Prueba del analizador de llamadas con el JSON generado
"""
import sys
import json

sys.path.append('.')
from src.log_processor.call_analyzer import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def test_analyzer():
    print("=== PROBANDO ANALIZADOR DE LLAMADAS ===\n")
    
    # Usar el JSON que ya generamos
    json_path = './temp/resultados.json'
    
    try:
        # Leer algunos casos del JSON
        with open(json_path, 'r', encoding='utf-8') as f:
            casos = []
            for line in f:
                if line.strip():
                    data = json.loads(line)
                    casos.append(data['numero_caso'])
        
        print(f"Casos encontrados en JSON: {len(casos)}")
        print(f"Primeros 5 casos: {casos[:5]}")
        print()
        
        # Crear analizador
        analizador = AnalizadorLlamadas('./full_log')
        
        # Probar análisis individual
        print("=== ANÁLISIS INDIVIDUAL ===")
        for i, caso in enumerate(casos[:3]):
            print(f"{i+1}. Analizando caso: {caso}")
            resultado = analizador.analizar_llamada(caso)
            print(f"   Estado: {resultado['estado']}")
            print(f"   Realizada: {resultado['realizada']}")
            print(f"   Calificación: {resultado['calificacion']}")
            print(f"   Acción: {resultado['accion']}")
            print()
        
        # Probar análisis múltiple
        print("=== ANÁLISIS MÚLTIPLE ===")
        resultados = analizador.analizar_multiples_llamadas(casos[:5])
        
        for caso, resultado in resultados.items():
            print(f"Caso {caso}: {resultado['estado']} - Cal: {resultado['calificacion']}")
        
        # Generar reporte
        print("\n=== REPORTE DE ESTADO ===")
        reporte = analizador.generar_reporte_estado(casos)
        
        stats = reporte['estadisticas']
        print(f"Total casos: {stats['total_casos']}")
        print(f"Completados: {stats['completados']}")
        print(f"Iniciados: {stats['iniciados']}")
        print(f"No encontrados: {stats['no_encontrados']}")
        print(f"Con calificación: {stats['con_calificacion']}")
        print(f"Calificaciones altas: {stats['calificaciones_altas']}")
        
        print("\n✅ Analizador funcionando correctamente!")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_analyzer()
