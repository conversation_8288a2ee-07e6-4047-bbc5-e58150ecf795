#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API REST con Flask para el Sistema de Reportes de Voicebot
Expone endpoints para generar y enviar reportes por email
"""
import sys
import os
from datetime import datetime
from threading import Thread
import json

# Agregar el directorio actual al path para imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
import logging

from main import VoicebotReporter
from config.settings import get_current_date, EMAIL_CONFIG, FILE_CONFIG
from src.reports.email_sender import EmailSender


# Crear aplicación Flask
app = Flask(__name__)
CORS(app)  # Habilitar CORS para requests desde frontend

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@app.route('/', methods=['GET'])
def root():
    """Endpoint raíz con información de la API"""
    return jsonify({
        "message": "Sistema de Reportes Voicebot API",
        "version": "1.0.0",
        "endpoints": {
            "health": "/health",
            "send_email": "/send-email [POST]",
            "generate_report": "/generate-report [POST]",
            "get_statistics": "/statistics [GET]",
            "download_report": "/download-report/<fecha> [GET]"
        }
    })


@app.route('/health', methods=['GET'])
def health_check():
    """Endpoint de health check"""
    try:
        # Verificar componentes básicos
        reporter = VoicebotReporter()
        if reporter.initialize_components():
            return jsonify({
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "components": {
                    "mongodb": "ok",
                    "email": "ok",
                    "log_processor": "ok"
                }
            })
        else:
            return jsonify({"status": "unhealthy", "error": "Components not available"}), 503
    except Exception as e:
        return jsonify({"status": "unhealthy", "error": str(e)}), 503


@app.route('/send-email', methods=['POST'])
def send_email():
    """
    Endpoint para enviar emails con reportes
    
    JSON Body:
    {
        "to": "<EMAIL>",
        "subject": "Asunto del email",
        "message": "Mensaje del email",
        "fecha": "2025-01-01" (opcional),
        "include_report": true (opcional)
    }
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({"error": "JSON body requerido"}), 400
        
        # Validar campos requeridos
        required_fields = ['to', 'subject', 'message']
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"Campo requerido: {field}"}), 400
        
        fecha = data.get('fecha', get_current_date())
        include_report = data.get('include_report', True)
        
        # Validar formato de fecha si se proporciona
        if 'fecha' in data:
            try:
                datetime.strptime(data['fecha'], '%Y-%m-%d')
            except ValueError:
                return jsonify({"error": "Formato de fecha inválido. Use YYYY-MM-DD"}), 400
        
        # Enviar email en background
        thread = Thread(
            target=_send_email_background,
            args=(data['to'], data['subject'], data['message'], fecha, include_report)
        )
        thread.daemon = True
        thread.start()
        
        return jsonify({
            "success": True,
            "message": "Email programado para envío",
            "email_sent_to": data['to']
        })
        
    except Exception as e:
        logger.error(f"Error enviando email: {e}")
        return jsonify({"error": f"Error enviando email: {str(e)}"}), 500


@app.route('/generate-report', methods=['POST'])
def generate_report():
    """
    Endpoint para generar reportes completos
    
    JSON Body:
    {
        "fecha": "2025-01-01" (opcional),
        "send_email": true (opcional),
        "copy_logs": true (opcional)
    }
    """
    try:
        data = request.get_json() or {}
        
        fecha = data.get('fecha', get_current_date())
        send_email = data.get('send_email', True)
        copy_logs = data.get('copy_logs', True)
        
        # Validar formato de fecha
        if 'fecha' in data:
            try:
                datetime.strptime(data['fecha'], '%Y-%m-%d')
            except ValueError:
                return jsonify({"error": "Formato de fecha inválido. Use YYYY-MM-DD"}), 400
        
        # Generar reporte
        reporter = VoicebotReporter(fecha)
        success = reporter.run_full_process(copy_logs=copy_logs, send_email=send_email)
        
        if success:
            # Obtener estadísticas
            stats = reporter.generate_statistics_report()
            
            # Construir ruta del reporte
            report_path = os.path.join(FILE_CONFIG['output_directory'], f"{fecha}.xlsx")
            
            return jsonify({
                "success": True,
                "message": "Reporte generado exitosamente",
                "report_path": report_path,
                "fecha": fecha,
                "statistics": stats.get("estadisticas", {})
            })
        else:
            return jsonify({"error": "Error generando reporte"}), 500
            
    except Exception as e:
        logger.error(f"Error generando reporte: {e}")
        return jsonify({"error": f"Error generando reporte: {str(e)}"}), 500


@app.route('/statistics', methods=['GET'])
def get_statistics():
    """
    Endpoint para obtener estadísticas de llamadas
    
    Query Parameters:
    - fecha: Fecha en formato YYYY-MM-DD (opcional)
    """
    try:
        fecha = request.args.get('fecha', get_current_date())
        
        # Validar formato de fecha
        try:
            datetime.strptime(fecha, '%Y-%m-%d')
        except ValueError:
            return jsonify({"error": "Formato de fecha inválido. Use YYYY-MM-DD"}), 400
        
        # Generar estadísticas
        reporter = VoicebotReporter(fecha)
        if reporter.initialize_components():
            stats = reporter.generate_statistics_report()
            
            return jsonify({
                "fecha": fecha,
                "estadisticas": stats.get("estadisticas", {}),
                "timestamp": datetime.now().isoformat()
            })
        else:
            return jsonify({"error": "Error inicializando componentes"}), 500
        
    except Exception as e:
        logger.error(f"Error obteniendo estadísticas: {e}")
        return jsonify({"error": f"Error obteniendo estadísticas: {str(e)}"}), 500


@app.route('/download-report/<fecha>', methods=['GET'])
def download_report(fecha):
    """
    Endpoint para descargar reportes Excel
    
    Args:
        fecha: Fecha del reporte en formato YYYY-MM-DD
    """
    try:
        # Validar formato de fecha
        try:
            datetime.strptime(fecha, '%Y-%m-%d')
        except ValueError:
            return jsonify({"error": "Formato de fecha inválido. Use YYYY-MM-DD"}), 400
        
        # Construir ruta del archivo
        file_path = os.path.join(FILE_CONFIG['output_directory'], f"{fecha}.xlsx")
        
        if not os.path.exists(file_path):
            return jsonify({"error": "Reporte no encontrado"}), 404
        
        return send_file(
            file_path,
            as_attachment=True,
            download_name=f"reporte_voicebot_{fecha}.xlsx",
            mimetype="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
        
    except Exception as e:
        logger.error(f"Error descargando reporte: {e}")
        return jsonify({"error": f"Error descargando reporte: {str(e)}"}), 500


@app.route('/test-email', methods=['POST'])
def test_email():
    """
    Endpoint para probar configuración de email
    
    JSON Body:
    {
        "to": "<EMAIL>" (opcional)
    }
    """
    try:
        data = request.get_json() or {}
        to = data.get('to', EMAIL_CONFIG['recipients'][0])
        
        email_sender = EmailSender()
        success = email_sender.send_test_email(to)
        
        if success:
            return jsonify({
                "success": True,
                "message": "Email de prueba enviado exitosamente",
                "sent_to": to
            })
        else:
            return jsonify({"error": "Error enviando email de prueba"}), 500
            
    except Exception as e:
        logger.error(f"Error en test de email: {e}")
        return jsonify({"error": f"Error en test de email: {str(e)}"}), 500


# Funciones auxiliares
def _send_email_background(to: str, subject: str, message: str, fecha: str, include_report: bool):
    """Envía email en background"""
    try:
        email_sender = EmailSender()
        
        if include_report:
            # Generar reporte si se solicita
            reporter = VoicebotReporter(fecha)
            if reporter.initialize_components():
                report_path = reporter.generate_report(send_email=False)
                if report_path:
                    email_sender.send_report(
                        to=to,
                        subject=subject,
                        message=message,
                        file_paths=[report_path]
                    )
                else:
                    email_sender.send_report(to=to, subject=subject, message=message)
            else:
                email_sender.send_report(to=to, subject=subject, message=message)
        else:
            email_sender.send_report(to=to, subject=subject, message=message)
            
    except Exception as e:
        logger.error(f"Error en envío de email background: {e}")


# Manejo de errores globales
@app.errorhandler(404)
def not_found(error):
    return jsonify({"error": "Endpoint no encontrado"}), 404


@app.errorhandler(500)
def internal_error(error):
    return jsonify({"error": "Error interno del servidor"}), 500


if __name__ == "__main__":
    # Configuración para desarrollo
    app.run(
        host="0.0.0.0",
        port=5000,
        debug=True
    )
