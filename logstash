input {
  file {
    path => "/var/log/app/full_log"  # Ruta al archivo de logs
    start_position => "beginning"
    sincedb_path => "/dev/null"
  }
}

filter {
  # Expresión regular para capturar los datos relevantes
  grok {
    match => { "message" => "Executing .* AGI\(.*?, \"reporte\.py, \"%{NUMBER:numero_caso}\", \"%{DATA:accion}\", \"%{NUMBER:calificacion}\"" }
  }

  # Si el evento tiene errores de parseo, lo descartamos
  if "_grokparsefailure" in [tags] {
    drop { }
  }
}

output {
  # Guardar solo los eventos correctamente procesados
  file {
    path => "/var/log/app/resultados.json"
    codec => json_lines
  }

  # Imprimir los resultados válidos en la consola
  stdout {
    codec => rubydebug
  }
}