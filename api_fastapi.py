#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API REST con FastAPI para el Sistema de Reportes de Voicebot
Expone endpoints para generar y enviar reportes por email
"""
import sys
import os
from datetime import datetime
from typing import Optional, List
import asyncio
from concurrent.futures import ThreadPoolExecutor

# Agregar el directorio actual al path para imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fastapi import FastAPI, HTTPException, BackgroundTasks, Query
from fastapi.responses import JSONResponse, FileResponse
from pydantic import BaseModel, EmailStr
import uvicorn

from main import VoicebotReporter
from config.settings import get_current_date, EMAIL_CONFIG
from src.reports.email_sender import EmailSender


# Modelos Pydantic para requests/responses
class EmailRequest(BaseModel):
    to: str
    subject: str
    message: str
    fecha: Optional[str] = None
    include_report: bool = True


class ReportRequest(BaseModel):
    fecha: Optional[str] = None
    send_email: bool = True
    copy_logs: bool = True


class EmailResponse(BaseModel):
    success: bool
    message: str
    email_sent_to: Optional[str] = None


class ReportResponse(BaseModel):
    success: bool
    message: str
    report_path: Optional[str] = None
    fecha: str
    statistics: Optional[dict] = None


# Crear aplicación FastAPI
app = FastAPI(
    title="Sistema de Reportes Voicebot API",
    description="API REST para generar reportes de Voicebot y enviar por email",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Executor para tareas en background
executor = ThreadPoolExecutor(max_workers=3)


@app.get("/", response_model=dict)
async def root():
    """Endpoint raíz con información de la API"""
    return {
        "message": "Sistema de Reportes Voicebot API",
        "version": "1.0.0",
        "endpoints": {
            "docs": "/docs",
            "health": "/health",
            "send_email": "/send-email",
            "generate_report": "/generate-report",
            "get_statistics": "/statistics",
            "download_report": "/download-report/{fecha}"
        }
    }


@app.get("/health", response_model=dict)
async def health_check():
    """Endpoint de health check"""
    try:
        # Verificar componentes básicos
        reporter = VoicebotReporter()
        if reporter.initialize_components():
            return {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "components": {
                    "mongodb": "ok",
                    "email": "ok",
                    "log_processor": "ok"
                }
            }
        else:
            raise HTTPException(status_code=503, detail="Service components not available")
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Service unhealthy: {str(e)}")


@app.post("/send-email", response_model=EmailResponse)
async def send_email(email_request: EmailRequest, background_tasks: BackgroundTasks):
    """
    Endpoint para enviar emails con reportes
    
    Args:
        email_request: Datos del email a enviar
        background_tasks: Tareas en background de FastAPI
    
    Returns:
        Respuesta con el estado del envío
    """
    try:
        fecha = email_request.fecha or get_current_date()
        
        # Validar formato de fecha si se proporciona
        if email_request.fecha:
            try:
                datetime.strptime(email_request.fecha, '%Y-%m-%d')
            except ValueError:
                raise HTTPException(status_code=400, detail="Formato de fecha inválido. Use YYYY-MM-DD")
        
        # Enviar email en background
        background_tasks.add_task(
            _send_email_background,
            email_request.to,
            email_request.subject,
            email_request.message,
            fecha,
            email_request.include_report
        )
        
        return EmailResponse(
            success=True,
            message="Email programado para envío",
            email_sent_to=email_request.to
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error enviando email: {str(e)}")


@app.post("/generate-report", response_model=ReportResponse)
async def generate_report(report_request: ReportRequest):
    """
    Endpoint para generar reportes completos
    
    Args:
        report_request: Parámetros para generar el reporte
    
    Returns:
        Respuesta con información del reporte generado
    """
    try:
        fecha = report_request.fecha or get_current_date()
        
        # Validar formato de fecha
        if report_request.fecha:
            try:
                datetime.strptime(report_request.fecha, '%Y-%m-%d')
            except ValueError:
                raise HTTPException(status_code=400, detail="Formato de fecha inválido. Use YYYY-MM-DD")
        
        # Ejecutar generación de reporte en thread pool
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            executor,
            _generate_report_sync,
            fecha,
            report_request.send_email,
            report_request.copy_logs
        )
        
        if result["success"]:
            return ReportResponse(
                success=True,
                message="Reporte generado exitosamente",
                report_path=result["report_path"],
                fecha=fecha,
                statistics=result.get("statistics")
            )
        else:
            raise HTTPException(status_code=500, detail=result["error"])
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generando reporte: {str(e)}")


@app.get("/statistics", response_model=dict)
async def get_statistics(fecha: Optional[str] = Query(None, description="Fecha en formato YYYY-MM-DD")):
    """
    Endpoint para obtener estadísticas de llamadas
    
    Args:
        fecha: Fecha para las estadísticas (opcional, por defecto hoy)
    
    Returns:
        Estadísticas de llamadas
    """
    try:
        fecha = fecha or get_current_date()
        
        # Validar formato de fecha
        if fecha:
            try:
                datetime.strptime(fecha, '%Y-%m-%d')
            except ValueError:
                raise HTTPException(status_code=400, detail="Formato de fecha inválido. Use YYYY-MM-DD")
        
        # Generar estadísticas en thread pool
        loop = asyncio.get_event_loop()
        stats = await loop.run_in_executor(
            executor,
            _get_statistics_sync,
            fecha
        )
        
        return {
            "fecha": fecha,
            "estadisticas": stats.get("estadisticas", {}),
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error obteniendo estadísticas: {str(e)}")


@app.get("/download-report/{fecha}")
async def download_report(fecha: str):
    """
    Endpoint para descargar reportes Excel
    
    Args:
        fecha: Fecha del reporte en formato YYYY-MM-DD
    
    Returns:
        Archivo Excel del reporte
    """
    try:
        # Validar formato de fecha
        try:
            datetime.strptime(fecha, '%Y-%m-%d')
        except ValueError:
            raise HTTPException(status_code=400, detail="Formato de fecha inválido. Use YYYY-MM-DD")
        
        # Construir ruta del archivo
        from config.settings import FILE_CONFIG
        file_path = os.path.join(FILE_CONFIG['output_directory'], f"{fecha}.xlsx")
        
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="Reporte no encontrado")
        
        return FileResponse(
            path=file_path,
            filename=f"reporte_voicebot_{fecha}.xlsx",
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error descargando reporte: {str(e)}")


# Funciones auxiliares para ejecutar en background/thread pool
async def _send_email_background(to: str, subject: str, message: str, fecha: str, include_report: bool):
    """Envía email en background"""
    try:
        email_sender = EmailSender()
        
        if include_report:
            # Generar reporte si se solicita
            reporter = VoicebotReporter(fecha)
            if reporter.initialize_components():
                report_path = reporter.generate_report(send_email=False)
                if report_path:
                    email_sender.send_report(
                        to=to,
                        subject=subject,
                        message=message,
                        file_paths=[report_path]
                    )
                else:
                    email_sender.send_report(to=to, subject=subject, message=message)
            else:
                email_sender.send_report(to=to, subject=subject, message=message)
        else:
            email_sender.send_report(to=to, subject=subject, message=message)
            
    except Exception as e:
        print(f"Error en envío de email background: {e}")


def _generate_report_sync(fecha: str, send_email: bool, copy_logs: bool) -> dict:
    """Genera reporte de forma síncrona"""
    try:
        reporter = VoicebotReporter(fecha)
        success = reporter.run_full_process(copy_logs=copy_logs, send_email=send_email)
        
        if success:
            # Obtener estadísticas
            stats = reporter.generate_statistics_report()
            
            # Construir ruta del reporte
            from config.settings import FILE_CONFIG
            report_path = os.path.join(FILE_CONFIG['output_directory'], f"{fecha}.xlsx")
            
            return {
                "success": True,
                "report_path": report_path,
                "statistics": stats.get("estadisticas", {})
            }
        else:
            return {"success": False, "error": "Error generando reporte"}
            
    except Exception as e:
        return {"success": False, "error": str(e)}


def _get_statistics_sync(fecha: str) -> dict:
    """Obtiene estadísticas de forma síncrona"""
    try:
        reporter = VoicebotReporter(fecha)
        if reporter.initialize_components():
            return reporter.generate_statistics_report()
        else:
            return {}
    except Exception as e:
        print(f"Error obteniendo estadísticas: {e}")
        return {}


if __name__ == "__main__":
    # Configuración para desarrollo
    uvicorn.run(
        "api_fastapi:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
