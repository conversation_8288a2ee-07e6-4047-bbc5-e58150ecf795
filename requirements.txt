# Dependencias del Sistema de Reportes de Voicebot

# Procesamiento de datos
pandas>=1.5.0
numpy>=1.21.0

# Base de datos
pymongo>=4.0.0

# Procesamiento de archivos
chardet>=5.0.0
openpyxl>=3.0.0

# Email
# smtplib y email están incluidos en la biblioteca estándar de Python

# API REST - FastAPI (recomendado)
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic[email]>=2.0.0

# API REST - Flask (alternativa)
flask>=2.3.0
flask-cors>=4.0.0

# Utilidades
python-dotenv>=0.19.0

# Desarrollo y testing (opcional)
pytest>=7.0.0
pytest-cov>=4.0.0
