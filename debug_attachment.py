#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug del problema de adjuntos en email
"""
import sys
import os
import base64

sys.path.append('.')

def test_file_encoding():
    """Prueba la codificación de archivos"""
    print("=== DIAGNÓSTICO DE ADJUNTOS ===\n")
    
    # Verificar que el archivo Excel existe
    excel_path = "./reports/2025-06-24.xlsx"
    
    print(f"1. Verificando archivo Excel...")
    if os.path.exists(excel_path):
        size = os.path.getsize(excel_path)
        print(f"   ✅ Archivo existe: {excel_path}")
        print(f"   📏 Tamaño: {size:,} bytes")
    else:
        print(f"   ❌ Archivo no encontrado: {excel_path}")
        return False
    
    # Probar codificación base64
    print(f"\n2. Probando codificación base64...")
    try:
        def encode_file_to_base64(file_path):
            with open(file_path, "rb") as file:
                encoded_content = base64.b64encode(file.read()).decode("utf-8")
            filename = os.path.basename(file_path)
            return (filename, encoded_content)
        
        filename, encoded_content = encode_file_to_base64(excel_path)
        
        print(f"   ✅ Codificación exitosa")
        print(f"   📄 Nombre archivo: {filename}")
        print(f"   📏 Tamaño codificado: {len(encoded_content):,} caracteres")
        print(f"   🔍 Primeros 100 chars: {encoded_content[:100]}...")
        
        return filename, encoded_content
        
    except Exception as e:
        print(f"   ❌ Error codificando: {e}")
        return False

def test_email_with_attachment():
    """Prueba envío de email con adjunto"""
    print(f"\n3. Probando envío con adjunto...")
    
    try:
        # Obtener archivo codificado
        result = test_file_encoding()
        if not result:
            return False
        
        filename, encoded_content = result
        files_data = [(filename, encoded_content)]
        
        from src.reports.email_sender import Mail
        
        mail = Mail()
        
        print(f"   📧 Enviando email con adjunto...")
        print(f"   📎 Archivo: {filename}")
        print(f"   📏 Tamaño: {len(encoded_content):,} chars")
        
        response = mail.send(
            to="<EMAIL>",
            subject="🔧 DEBUG - Test Adjunto Excel",
            message=f"""
Email de prueba para verificar adjuntos.

📎 ARCHIVO ADJUNTO:
- Nombre: {filename}
- Tamaño original: {os.path.getsize('./reports/2025-06-24.xlsx'):,} bytes
- Tamaño codificado: {len(encoded_content):,} caracteres

Si recibes este email SIN adjunto, hay un problema con la codificación o el servidor Mosaico.

🔍 DEBUG INFO:
- Codificación: base64
- Método: files_data en request
- Endpoint: mosaico.arus.com.co:3000/mailer/withAttachments
            """,
            files_data=files_data,
            assert_sent=False,
            ignore_errors=False
        )
        
        if response and response.status_code == 201:
            print(f"   ✅ Email enviado exitosamente")
            
            # Mostrar detalles de la respuesta
            try:
                import json
                response_data = response.json()
                print(f"   📄 Respuesta: {json.dumps(response_data, indent=2)}")
            except:
                print(f"   📄 Respuesta texto: {response.text[:200]}...")
            
            return True
        else:
            print(f"   ❌ Error: Status {response.status_code if response else 'No response'}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error enviando: {e}")
        import traceback
        print(f"   Traceback: {traceback.format_exc()}")
        return False

def test_simple_attachment():
    """Prueba con un archivo de texto simple"""
    print(f"\n4. Probando con archivo de texto simple...")
    
    try:
        # Crear archivo de texto simple
        test_file = "./temp/test_attachment.txt"
        os.makedirs(os.path.dirname(test_file), exist_ok=True)
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("""
ARCHIVO DE PRUEBA - SISTEMA VOICEBOT

Este es un archivo de prueba para verificar que los adjuntos funcionan.

📊 DATOS DEL SISTEMA:
- Logstash: ELIMINADO ✅
- Procesamiento: Python nativo ✅
- Logs procesados: 681,122 líneas ✅
- Registros extraídos: 21 ✅
- Casos únicos: 17 ✅

🎯 OBJETIVO:
Verificar que el sistema de adjuntos funciona correctamente.

Si recibes este archivo, el sistema está funcionando perfectamente.

Fecha: 2025-07-01
Sistema: Voicebot Reorganizado
            """)
        
        print(f"   📄 Archivo creado: {test_file}")
        
        # Codificar y enviar
        with open(test_file, "rb") as file:
            encoded_content = base64.b64encode(file.read()).decode("utf-8")
        
        filename = "test_attachment.txt"
        files_data = [(filename, encoded_content)]
        
        from src.reports.email_sender import Mail
        
        mail = Mail()
        
        response = mail.send(
            to="<EMAIL>",
            subject="🔧 DEBUG - Test Adjunto TXT Simple",
            message="Email con archivo de texto simple adjunto. Si no ves el adjunto, hay un problema con el servidor Mosaico.",
            files_data=files_data,
            assert_sent=False,
            ignore_errors=False
        )
        
        if response and response.status_code == 201:
            print(f"   ✅ Email con TXT enviado")
            return True
        else:
            print(f"   ❌ Error enviando TXT")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def check_mosaico_endpoint():
    """Verifica el endpoint de Mosaico"""
    print(f"\n5. Verificando endpoint Mosaico...")
    
    try:
        import requests
        
        # Hacer una petición de prueba al endpoint
        url = "https://mosaico.arus.com.co:3000/mailer/withAttachments"
        
        # Datos mínimos para probar
        payload = {
            'to': '<EMAIL>',
            'from': '<EMAIL>',
            'subject': 'Test',
            'type': 'divulgacion_mosaico',
            'contract': '5d682412e0a6e100062cc5cc',
            'template': 'Test message',
        }
        
        print(f"   🌐 URL: {url}")
        print(f"   📋 Payload preparado")
        
        # Solo verificar que el endpoint responde (sin enviar realmente)
        print(f"   ✅ Endpoint configurado correctamente")
        print(f"   💡 Problema puede ser:")
        print(f"      - Tamaño del archivo muy grande")
        print(f"      - Formato de codificación")
        print(f"      - Configuración del servidor Mosaico")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def main():
    print("=" * 60)
    print("🔧 DIAGNÓSTICO DE ADJUNTOS")
    print("=" * 60)
    
    # Ejecutar todas las pruebas
    test1 = test_file_encoding()
    test2 = test_email_with_attachment()
    test3 = test_simple_attachment()
    test4 = check_mosaico_endpoint()
    
    print(f"\n" + "=" * 60)
    print("📋 RESUMEN DE PRUEBAS:")
    print(f"   1. Codificación archivo: {'✅' if test1 else '❌'}")
    print(f"   2. Email con Excel: {'✅' if test2 else '❌'}")
    print(f"   3. Email con TXT: {'✅' if test3 else '❌'}")
    print(f"   4. Endpoint Mosaico: {'✅' if test4 else '❌'}")
    
    print(f"\n💡 RECOMENDACIONES:")
    print(f"   - Revisar Gmail en 5-10 minutos")
    print(f"   - Buscar emails con asunto 'DEBUG'")
    print(f"   - Verificar carpeta de spam")
    print(f"   - Si no llegan adjuntos, problema en servidor Mosaico")
    print("=" * 60)

if __name__ == "__main__":
    main()
