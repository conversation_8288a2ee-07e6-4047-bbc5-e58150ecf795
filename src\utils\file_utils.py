# -*- coding: utf-8 -*-
"""
Utilidades para manejo de archivos
"""
import base64
import os
from typing import <PERSON>ple, List


def encode_file_to_base64(file_path: str) -> Tuple[str, str]:
    """
    Convierte un archivo a base64 y devuelve un tuple con el nombre y el contenido codificado
    
    Args:
        file_path: Ruta del archivo a codificar
        
    Returns:
        <PERSON><PERSON> (nombre_archivo, contenido_base64)
    """
    try:
        with open(file_path, "rb") as file:
            encoded_content = base64.b64encode(file.read()).decode("utf-8")
        
        filename = os.path.basename(file_path)
        return (filename, encoded_content)
        
    except Exception as e:
        print(f"Error codificando archivo {file_path}: {e}")
        raise


def encode_multiple_files(file_paths: List[str]) -> List[Tuple[str, str]]:
    """
    Codifica múltiples archivos a base64
    
    Args:
        file_paths: Lista de rutas de archivos
        
    Returns:
        Lista de tuples (nombre_archivo, contenido_base64)
    """
    encoded_files = []
    
    for file_path in file_paths:
        try:
            encoded_file = encode_file_to_base64(file_path)
            encoded_files.append(encoded_file)
            print(f"Archivo codificado: {file_path}")
        except Exception as e:
            print(f"Error codificando {file_path}: {e}")
            continue
    
    return encoded_files


def ensure_directory_exists(directory_path: str) -> bool:
    """
    Asegura que un directorio existe, creándolo si es necesario
    
    Args:
        directory_path: Ruta del directorio
        
    Returns:
        True si el directorio existe o se creó exitosamente
    """
    try:
        os.makedirs(directory_path, exist_ok=True)
        return True
    except Exception as e:
        print(f"Error creando directorio {directory_path}: {e}")
        return False


def file_exists(file_path: str) -> bool:
    """
    Verifica si un archivo existe
    
    Args:
        file_path: Ruta del archivo
        
    Returns:
        True si el archivo existe
    """
    return os.path.isfile(file_path)


def get_file_size(file_path: str) -> int:
    """
    Obtiene el tamaño de un archivo en bytes
    
    Args:
        file_path: Ruta del archivo
        
    Returns:
        Tamaño del archivo en bytes, -1 si hay error
    """
    try:
        return os.path.getsize(file_path)
    except Exception as e:
        print(f"Error obteniendo tamaño de {file_path}: {e}")
        return -1


def clean_temp_files(temp_directory: str, pattern: str = None) -> int:
    """
    Limpia archivos temporales de un directorio
    
    Args:
        temp_directory: Directorio temporal
        pattern: Patrón de archivos a eliminar (opcional)
        
    Returns:
        Número de archivos eliminados
    """
    import glob
    
    try:
        if pattern:
            search_pattern = os.path.join(temp_directory, pattern)
        else:
            search_pattern = os.path.join(temp_directory, "*")
        
        files_to_delete = glob.glob(search_pattern)
        deleted_count = 0
        
        for file_path in files_to_delete:
            try:
                if os.path.isfile(file_path):
                    os.remove(file_path)
                    deleted_count += 1
                    print(f"Archivo eliminado: {file_path}")
            except Exception as e:
                print(f"Error eliminando {file_path}: {e}")
        
        return deleted_count
        
    except Exception as e:
        print(f"Error limpiando archivos temporales: {e}")
        return 0
