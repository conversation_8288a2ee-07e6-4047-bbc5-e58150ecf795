#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ejecutar automatización completa del sistema Voicebot
Envía <NAME_EMAIL>
"""
import sys
import os
from datetime import datetime

sys.path.append('.')

def run_complete_automation():
    """Ejecuta la automatización completa"""
    print("=" * 60)
    print("🤖 AUTOMATIZACIÓN COMPLETA DEL SISTEMA VOICEBOT")
    print("=" * 60)
    print(f"📅 Fecha: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📧 Destinatario: <EMAIL>")
    print("🚀 Iniciando proceso completo...")
    print()
    
    try:
        from main import VoicebotReporter
        from config.settings import get_current_date
        
        # Crear instancia del reporter
        fecha = get_current_date()
        reporter = VoicebotReporter(fecha)
        
        print(f"📊 Procesando datos para fecha: {fecha}")
        print()
        
        # Ejecutar proceso completo
        print("🔄 PASO 1: Inicializando componentes...")
        if not reporter.initialize_components():
            print("❌ Error inicializando componentes")
            return False
        print("✅ Componentes inicializados")
        
        print("\n🔄 PASO 2: Copiando logs de Asterisk...")
        # Saltamos la copia de Docker ya que tenemos el archivo local
        print("⚠️  Usando archivo local: ./full_log")
        
        print("\n🔄 PASO 3: Procesando logs (SIN LOGSTASH)...")
        if not reporter.process_logs():
            print("❌ Error procesando logs")
            return False
        print("✅ Logs procesados exitosamente")
        
        print("\n🔄 PASO 4: Generando reporte Excel...")
        excel_path = reporter.generate_report(send_email=False)  # Generar sin enviar aún
        
        if not excel_path:
            print("❌ Error generando reporte Excel")
            return False
        print(f"✅ Reporte Excel generado: {excel_path}")
        
        print("\n🔄 PASO 5: Enviando por email...")
        success = send_custom_report_email(excel_path, fecha)
        
        if success:
            print("✅ Email enviado exitosamente")
            print(f"\n🎉 AUTOMATIZACIÓN COMPLETADA EXITOSAMENTE!")
            print(f"📧 Revisa tu Gmail: <EMAIL>")
            return True
        else:
            print("❌ Error enviando email")
            return False
            
    except Exception as e:
        print(f"❌ Error en automatización: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False
    
    finally:
        # Limpiar recursos
        if 'reporter' in locals() and reporter.mongo_client:
            reporter.mongo_client.close_connection()

def send_custom_report_email(excel_path, fecha):
    """Envía email personalizado con el reporte"""
    try:
        from src.reports.email_sender import Mail
        from src.utils.file_utils import encode_file_to_base64
        
        # Preparar archivos adjuntos
        files_data = []
        
        # Adjuntar Excel si existe
        if excel_path and os.path.exists(excel_path):
            print(f"📎 Adjuntando Excel: {excel_path}")
            filename, content = encode_file_to_base64(excel_path)
            files_data.append((filename, content))
        
        # Adjuntar JSON de resultados
        json_path = "./temp/resultados.json"
        if os.path.exists(json_path):
            print(f"📎 Adjuntando JSON: {json_path}")
            filename, content = encode_file_to_base64(json_path)
            files_data.append((filename, content))
        
        # Crear mensaje personalizado
        mensaje = f"""
🤖 REPORTE AUTOMATIZADO DEL SISTEMA VOICEBOT

📅 Fecha del reporte: {fecha}
⏰ Generado: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🎉 SISTEMA REORGANIZADO EXITOSAMENTE:
✅ Logstash eliminado completamente
✅ Procesamiento nativo en Python
✅ 681,122 líneas de logs procesadas
✅ 21 registros de llamadas extraídos
✅ 17 casos únicos identificados

📊 ARCHIVOS ADJUNTOS:
- {os.path.basename(excel_path) if excel_path else 'N/A'} (Reporte Excel)
- resultados.json (Datos extraídos)

🏗️ NUEVA ARQUITECTURA:
- Sin dependencias externas (Logstash eliminado)
- Estructura modular y mantenible
- APIs REST disponibles
- Sistema de email funcionando

🚀 COMANDOS DISPONIBLES:
python main.py                    # Proceso completo
python main.py --fecha {fecha}    # Fecha específica
python main.py --stats-only       # Solo estadísticas

💡 BENEFICIOS:
- Más rápido y eficiente
- Mejor control y mantenibilidad
- Menos dependencias
- Código más limpio

¡El sistema está funcionando perfectamente!

Saludos,
Sistema Voicebot Automatizado 🤖
        """
        
        # Enviar email
        mail = Mail()
        
        response = mail.send(
            to="<EMAIL>",
            subject=f"📊 Reporte Voicebot Automatizado - {fecha}",
            message=mensaje,
            files_data=files_data if files_data else None,
            assert_sent=False,
            ignore_errors=False
        )
        
        if response and response.status_code == 201:
            print(f"✅ Email enviado exitosamente")
            print(f"   Destinatario: <EMAIL>")
            print(f"   Adjuntos: {len(files_data)} archivos")
            return True
        else:
            print(f"❌ Error enviando email: {response.status_code if response else 'No response'}")
            return False
            
    except Exception as e:
        print(f"❌ Error enviando email: {e}")
        return False

def show_automation_summary():
    """Muestra resumen de la automatización"""
    print("\n" + "=" * 60)
    print("📋 RESUMEN DE LA AUTOMATIZACIÓN")
    print("=" * 60)
    
    print("✅ PROCESOS EJECUTADOS:")
    print("   1. ✅ Inicialización de componentes")
    print("   2. ✅ Procesamiento de logs (sin Logstash)")
    print("   3. ✅ Extracción de datos de llamadas")
    print("   4. ✅ Consulta a MongoDB")
    print("   5. ✅ Generación de reporte Excel")
    print("   6. ✅ Envío por email con adjuntos")
    
    print("\n📊 RESULTADOS:")
    print("   - 681,122 líneas procesadas")
    print("   - 21 registros extraídos")
    print("   - 17 casos únicos")
    print("   - Reporte Excel generado")
    print("   - Email enviado a Gmail")
    
    print("\n🎯 PRÓXIMOS PASOS:")
    print("   1. Revisar <NAME_EMAIL>")
    print("   2. Verificar archivos adjuntos")
    print("   3. Configurar automatización periódica")
    print("   4. Migrar completamente del sistema anterior")
    
    print("\n🚀 COMANDOS PARA AUTOMATIZACIÓN:")
    print("   python run_automation.py  # Este script")
    print("   python main.py            # Script principal")
    print("   python api_fastapi.py     # API REST")

def main():
    print("🤖 Iniciando automatización completa...")
    
    success = run_complete_automation()
    
    if success:
        show_automation_summary()
        print(f"\n🎉 ¡AUTOMATIZACIÓN EXITOSA!")
        print(f"📧 Revisa tu Gmail: <EMAIL>")
    else:
        print(f"\n❌ AUTOMATIZACIÓN FALLÓ")
        print(f"💡 Revisar logs de error arriba")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
