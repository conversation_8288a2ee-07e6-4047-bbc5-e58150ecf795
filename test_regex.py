#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para probar los patrones regex con el log real
"""
import re

# Líneas de ejemplo del log
test_lines = [
    '[Jul  1 13:40:22] VERBOSE[66][C-00000000] pbx.c:     -- Executing [9020@call-file-test2:46] AGI("SIP/salidacel-00000000", "reporte.py, "7040578", "calificacion", "5"") in new stack',
    '[Jul  1 13:42:48] VERBOSE[114][C-00000001] pbx.c:     -- Executing [9020@call-file-test2:46] AGI("SIP/salidacel-00000001", "reporte.py, "6997086", "calificacion", """) in new stack',
    '[Jul  1 14:41:25] VERBOSE[1331][C-0000001c] pbx.c:     -- Executing [9020@call-file-test2:46] AGI("SIP/salidacel-0000001c", "reporte.py, "7033851", "calificacion", "9"") in new stack'
]

# Diferentes patrones a probar
patterns = {
    'pattern1': r'reporte\.py, \"([^\"]*)\", \"([^\"]*)\", \"([^\"]*)\"\"\)',
    'pattern2': r'AGI\([^,]+, \"reporte\.py, \"([^\"]*)\", \"([^\"]*)\", \"([^\"]*)\"\"\)',
    'pattern3': r'Executing .* AGI\([^,]+, \"reporte\.py, \"([^\"]*)\", \"([^\"]*)\", \"([^\"]*)\"\"\)',
    'pattern4': r'reporte\.py, \"(\d+)\", \"([^\"]*)\", \"([^\"]*)\"\"\)'
}

print("=== PRUEBA DE PATRONES REGEX ===\n")

for i, line in enumerate(test_lines, 1):
    print(f"LÍNEA {i}:")
    print(f"  {line}")
    print()
    
    for name, pattern in patterns.items():
        match = re.search(pattern, line)
        print(f"  {name}: ", end="")
        if match:
            print(f"✅ MATCH - Grupos: {match.groups()}")
            print(f"    Numero: '{match.group(1)}', Accion: '{match.group(2)}', Calificacion: '{match.group(3)}'")
        else:
            print("❌ NO MATCH")
    print("-" * 80)

print("\n=== PROBANDO CON ARCHIVO REAL (PRIMERAS 10 LÍNEAS CON reporte.py) ===")

# Leer algunas líneas del archivo real
try:
    with open('full_log', 'r', encoding='utf-8', errors='ignore') as f:
        count = 0
        for line_num, line in enumerate(f, 1):
            if 'reporte.py' in line and count < 10:
                count += 1
                print(f"\nLínea {line_num}: {line.strip()}")
                
                # Probar el mejor patrón
                best_pattern = r'reporte\.py, \"([^\"]*)\", \"([^\"]*)\", \"([^\"]*)\"\"\)'
                match = re.search(best_pattern, line)
                if match:
                    print(f"  ✅ Numero: '{match.group(1)}', Accion: '{match.group(2)}', Calificacion: '{match.group(3)}'")
                else:
                    print(f"  ❌ No match")
                    
except Exception as e:
    print(f"Error leyendo archivo: {e}")

print("\n=== FIN DE PRUEBAS ===")
