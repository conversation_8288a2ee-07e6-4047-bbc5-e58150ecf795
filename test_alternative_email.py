#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Probar email con destinatario alternativo
"""
import sys

sys.path.append('.')

def test_alternative_email():
    """Prueba con destinatario alternativo"""
    print("=== PRUEBA CON DESTINATARIO ALTERNATIVO ===\n")
    
    try:
        from src.reports.email_sender import Mail
        
        mail = Mail()
        
        # Probar con nataly.gil
        print("📧 <NAME_EMAIL>...")
        
        response = mail.send(
            to="<EMAIL>",
            subject="PRUEBA Sistema Voicebot - Destinatario Alternativo",
            message="Email de prueba para verificar si el sistema funciona con otros destinatarios. Si recibes esto, por favor confirma a Andrés Vélez.",
            assert_sent=False,
            ignore_errors=False
        )
        
        if response and response.status_code == 201:
            print("✅ Email <NAME_EMAIL>")
            return True
        else:
            print(f"❌ Error enviando a nataly.gil")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    print("=" * 50)
    print("PRUEBA CON DESTINATARIO ALTERNATIVO")
    print("=" * 50)
    
    success = test_alternative_email()
    
    print(f"\n" + "=" * 50)
    if success:
        print("✅ EMAIL ALTERNATIVO ENVIADO")
        print("📧 Revisar con Nataly Gil si llegó")
    else:
        print("❌ ERROR EN EMAIL ALTERNATIVO")
    print("=" * 50)

if __name__ == "__main__":
    main()
