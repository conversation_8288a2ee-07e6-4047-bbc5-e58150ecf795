# -*- coding: utf-8 -*-
"""
Cliente de MongoDB para el sistema de reportes
"""
import pymongo
import pandas as pd
from typing import List, Dict, Optional
from datetime import datetime
from config.settings import MONGODB_CONFIG, get_mongodb_uri, get_current_date


class MongoDBClient:
    """Cliente para operaciones con MongoDB"""
    
    def __init__(self):
        self.client = None
        self.db = None
        self._connect()
    
    def _connect(self):
        """Establece conexión con MongoDB"""
        try:
            uri = get_mongodb_uri()
            self.client = pymongo.MongoClient(uri)
            self.db = self.client[MONGODB_CONFIG['database']]
            # Verificar conexión
            self.client.admin.command('ping')
            print("Conexión a MongoDB establecida exitosamente")
        except Exception as e:
            print(f"Error conectando a MongoDB: {e}")
            raise
    
    def get_collection(self, collection_name: str):
        """Obtiene una colección específica"""
        if not self.db:
            raise Exception("No hay conexión a la base de datos")
        return self.db[collection_name]
    
    def get_casos_by_date(self, fecha: str = None) -> List[Dict]:
        """
        Obtiene todos los casos de una fecha específica
        
        Args:
            fecha: Fecha en formato YYYY-MM-DD. Si es None, usa la fecha actual
            
        Returns:
            Lista de documentos de casos
        """
        if fecha is None:
            fecha = get_current_date()
        
        try:
            collection = self.get_collection(fecha)
            data = list(collection.find({}))
            
            # Convertir ObjectId a string
            for record in data:
                record["_id"] = str(record["_id"])
            
            print(f"Obtenidos {len(data)} casos para la fecha {fecha}")
            return data
            
        except Exception as e:
            print(f"Error obteniendo casos para fecha {fecha}: {e}")
            return []
    
    def get_casos_dataframe(self, fecha: str = None) -> pd.DataFrame:
        """
        Obtiene casos como DataFrame de pandas
        
        Args:
            fecha: Fecha en formato YYYY-MM-DD
            
        Returns:
            DataFrame con los casos
        """
        data = self.get_casos_by_date(fecha)
        
        if not data:
            return pd.DataFrame()
        
        # Normalizar datos JSON a DataFrame
        df = pd.json_normalize(data)
        
        # Agregar columnas por defecto si no existen
        default_columns = {
            'calificacion': 'FALSO',
            'realizada': 'FALSO',
            'finalizo': 'FALSO'
        }
        
        for col, default_value in default_columns.items():
            if col not in df.columns:
                df[col] = default_value
        
        print(f"DataFrame creado con {len(df)} filas y {len(df.columns)} columnas")
        return df
    
    def get_casos_by_range(self, fecha_inicio: str, fecha_fin: str) -> List[Dict]:
        """
        Obtiene casos de un rango de fechas
        
        Args:
            fecha_inicio: Fecha de inicio en formato YYYY-MM-DD
            fecha_fin: Fecha de fin en formato YYYY-MM-DD
            
        Returns:
            Lista combinada de documentos
        """
        all_data = []
        
        try:
            # Generar lista de fechas en el rango
            start_date = datetime.strptime(fecha_inicio, '%Y-%m-%d')
            end_date = datetime.strptime(fecha_fin, '%Y-%m-%d')
            
            current_date = start_date
            while current_date <= end_date:
                fecha_str = current_date.strftime('%Y-%m-%d')
                data = self.get_casos_by_date(fecha_str)
                all_data.extend(data)
                current_date += pd.Timedelta(days=1)
            
            print(f"Obtenidos {len(all_data)} casos en total del rango {fecha_inicio} a {fecha_fin}")
            return all_data
            
        except Exception as e:
            print(f"Error obteniendo casos del rango {fecha_inicio} a {fecha_fin}: {e}")
            return []
    
    def insert_caso(self, caso_data: Dict, fecha: str = None) -> bool:
        """
        Inserta un nuevo caso
        
        Args:
            caso_data: Datos del caso a insertar
            fecha: Fecha de la colección. Si es None, usa fecha actual
            
        Returns:
            True si se insertó exitosamente
        """
        if fecha is None:
            fecha = get_current_date()
        
        try:
            collection = self.get_collection(fecha)
            result = collection.insert_one(caso_data)
            print(f"Caso insertado con ID: {result.inserted_id}")
            return True
        except Exception as e:
            print(f"Error insertando caso: {e}")
            return False
    
    def update_caso(self, caso_id: str, update_data: Dict, fecha: str = None) -> bool:
        """
        Actualiza un caso existente
        
        Args:
            caso_id: ID del caso a actualizar
            update_data: Datos a actualizar
            fecha: Fecha de la colección
            
        Returns:
            True si se actualizó exitosamente
        """
        if fecha is None:
            fecha = get_current_date()
        
        try:
            from bson import ObjectId
            collection = self.get_collection(fecha)
            result = collection.update_one(
                {"_id": ObjectId(caso_id)},
                {"$set": update_data}
            )
            print(f"Caso actualizado. Documentos modificados: {result.modified_count}")
            return result.modified_count > 0
        except Exception as e:
            print(f"Error actualizando caso: {e}")
            return False
    
    def get_available_collections(self) -> List[str]:
        """
        Obtiene lista de colecciones disponibles (fechas)
        
        Returns:
            Lista de nombres de colecciones
        """
        try:
            collections = self.db.list_collection_names()
            # Filtrar solo las que parecen fechas
            date_collections = [col for col in collections if self._is_date_format(col)]
            return sorted(date_collections)
        except Exception as e:
            print(f"Error obteniendo colecciones: {e}")
            return []
    
    def _is_date_format(self, text: str) -> bool:
        """Verifica si un texto tiene formato de fecha YYYY-MM-DD"""
        try:
            datetime.strptime(text, '%Y-%m-%d')
            return True
        except ValueError:
            return False
    
    def close_connection(self):
        """Cierra la conexión a MongoDB"""
        if self.client:
            self.client.close()
            print("Conexión a MongoDB cerrada")
    
    def __enter__(self):
        """Context manager entry"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.close_connection()
