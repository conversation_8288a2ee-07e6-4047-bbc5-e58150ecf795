# Configuración de Email
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=tu_contraseña_de_aplicacion
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587

# Configuración de MongoDB (opcional, sobrescribe config/settings.py)
MONGODB_HOST=***********
MONGODB_PORT=27017
MONGODB_USERNAME=admin
MONGODB_PASSWORD=V0iC3boatT
MONGODB_DATABASE=mydatabase

# Configuración de Docker (opcional)
DOCKER_CONTAINER_NAME=asterisk
DOCKER_LOG_SOURCE_PATH=/var/log/asterisk/full
DOCKER_LOG_DESTINATION_PATH=/tmp/full_log

# Configuración de archivos (opcional)
OUTPUT_DIRECTORY=/home/<USER>/reporte/
TEMP_DIRECTORY=/tmp/
