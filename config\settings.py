# -*- coding: utf-8 -*-
"""
Configuración del sistema de reportes de Voicebot
"""
import os
from datetime import datetime

# Configuración de MongoDB
MONGODB_CONFIG = {
    'host': '***********',
    'port': 27017,
    'username': 'admin',
    'password': 'V0iC3boatT',
    'database': 'mydatabase'
}

# Configuración de Docker
DOCKER_CONFIG = {
    'container_name': 'asterisk',
    'log_source_path': '/var/log/asterisk/full',
    'log_destination_path': '/tmp/full_log'
}

# Configuración de archivos
FILE_CONFIG = {
    'log_file_path': './full_log',
    'results_json_path': './temp/resultados.json',
    'output_directory': './reports/',
    'temp_directory': './temp/'
}

# Configuración de email
EMAIL_CONFIG = {
    'recipients': [
        '<EMAIL>',  # Gmail personal que funciona
        '<EMAIL>',  # Email corporativo (backup)
        # '<EMAIL>',
        # '<EMAIL>'
    ],
    'subject_template': 'Reporte Voicebot {date}',
    'message': 'Reporte automatizado del sistema Voicebot reorganizado (sin Logstash)'
}

# Configuración de reportes
REPORT_CONFIG = {
    'date_format': '%Y-%m-%d',
    'excel_columns': [
        '_id', 'telefono', 'descripcion', 'caso_db', 'nombre', 
        'apellido', 'realizada', 'calificacion', 'finalizo', 
        'reabrir', 'fecha', 'caso', 'asignado', 'grupo'
    ],
    'max_calificacion': 5
}

# Patrones de regex para logs
LOG_PATTERNS = {
    'agi_pattern': r'reporte\.py, \"{numero_caso}\", \"([^\"]*)\", \"([^\"]*)\"\"\)',
    'call_pattern': r'reporte\.py, \"{numero_caso}\", \"([^\"]*)\"\"\)'
}

def get_current_date():
    """Obtiene la fecha actual en formato YYYY-MM-DD"""
    return datetime.now().strftime(REPORT_CONFIG['date_format'])

def get_mongodb_uri():
    """Construye la URI de conexión a MongoDB"""
    config = MONGODB_CONFIG
    return f"mongodb://{config['username']}:{config['password']}@{config['host']}:{config['port']}/"
