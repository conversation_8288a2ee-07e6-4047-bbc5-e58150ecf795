# -*- coding: utf-8 -*-
"""
Sistema de envío de emails para reportes
"""
import base64
import requests
from typing import Text, List, Tuple, Union, Optional, cast, NewType, ByteString
from config.settings import EMAIL_CONFIG

# Deshabilitar warnings de SSL
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

RequestFiles = NewType('RequestFiles', List[Tuple[Text, Tuple[Text, ByteString]]])

class Mail:
    """Provides methods to generate mail templates and send mails."""

    __url: Text
    __type: Text
    __contract: Text

    def __init__(
        self,
        url=None,
        mail_type=None,
        contract=None,
    ) -> None:
        self.__url = url or EMAIL_CONFIG['url']
        self.__type = mail_type or EMAIL_CONFIG['mail_type']
        self.__contract = contract or EMAIL_CONFIG['contract']

    def __get_files(self, files_data: List[Tuple[Text, Union[Text, ByteString]]]) -> RequestFiles:
        """Decodes the base64-encoded files and creates the attachments payload.

        Parameters
        ----------
        files_data : List[Tuple[Text, Union[Text, ByteString]]]
            List of objects containing the name and base64-encoded data of each file.

        Returns
        -------
        RequestFiles
            Attachments payload.
        """

        return cast(
            RequestFiles,
            [('files', (name, base64.b64decode(data) if isinstance(data, str) else data)) for name, data in files_data],
        )

    def send(
        self,
        to: Text,
        subject: Text,
        message: Text,
        files_data: Optional[List[Tuple[Text, Union[Text, ByteString]]]] = None,
        assert_sent=True,
        ignore_errors=False,
        send_from=None,
    ):
        """Sends a mail.

        Parameters
        ----------
        to : Text
            Recipients.
        subject : Text
            Subject.
        message : Text
            Message.
        files_data : Optional[List[Tuple[Text, Union[Text, ByteString]]]], optional
            List of objects containing the name and base64-encoded data of each file, by default None.
        assert_sent : bool, optional
            Check if the mail was sent, by default True.
        ignore_errors : bool, optional
            Ignore errors, by default False.
        send_from : Text, optional
            Specify a different sender, by default from EMAIL_CONFIG.

        Raises
        ------
        Exception
            Mail could not be sent.
        """

        try:
            if not to:
                raise Exception('No se pudo enviar el correo. El destinatario es requerido')

            if not subject or not message:
                raise Exception('No se pudo enviar el correo. El asunto y el mensaje son requeridos')

            send_from = send_from or EMAIL_CONFIG['sender']

            payload = {
                'to': to,
                'from': send_from,
                'subject': subject,
                'type': self.__type,
                'contract': self.__contract,
                'template': message,
            }

            files = None
            if files_data:
                files = self.__get_files(files_data)

            response = requests.post(self.__url, data=payload, files=files, verify=False)
            response.raise_for_status()

            response_data = response.json()
            if assert_sent:
                assert (
                    'state' in response_data and response_data['state'] == 'sent'
                ), f'No se pudo enviar el correo: {response.text}'

            return response

        except Exception as e:
            if not ignore_errors:
                raise Exception(f'No se pudo enviar el correo: {str(e)}')


class EmailSender:
    """Clase simplificada para envío de emails"""
    
    def __init__(self):
        self.mail = Mail()
    
    def send_report(self, recipient: str, subject: str, message: str, excel_path: str = None) -> bool:
        """
        Envía un reporte por email
        
        Args:
            recipient: Email del destinatario
            subject: Asunto del email
            message: Mensaje del email
            excel_path: Ruta del archivo Excel a adjuntar
            
        Returns:
            True si se envió exitosamente, False en caso contrario
        """
        try:
            files_data = None
            
            if excel_path:
                # Codificar archivo Excel
                with open(excel_path, "rb") as file:
                    encoded_content = base64.b64encode(file.read()).decode("utf-8")
                    filename = excel_path.split("/")[-1].split("\\")[-1]
                    files_data = [(filename, encoded_content)]
            
            response = self.mail.send(
                to=recipient,
                subject=subject,
                message=message,
                files_data=files_data
            )
            
            return response and response.status_code == 201
            
        except Exception as e:
            print(f"Error enviando email: {e}")
            return False
