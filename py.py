import pandas as pd
import json
import json
import chardet
import pandas as pd
import pymongo
import json
import subprocess
from datetime import datetime
dat = datetime.now().strftime('%Y-%m-%d')
print(dat)
try:
    subprocess.run(["docker", "cp", "asterisk:/var/log/asterisk/full", "/tmp/full_log"], check=True)
    print("Docker copy command executed successfully.")
except subprocess.CalledProcessError as e:
    print(f"Error executing Docker command: {e}")
client = pymongo.MongoClient("********************************************/")  # Reemplaza con tu cadena de conexión
#exit()
# Selecciona la base de datos y la colección específica
db = client['mydatabase']
dat = '2025-06-24'  # Nombre de la colección específica
collection = db[dat]

# Encuentra todos los documentos en la colección
data = list(collection.find({}))

# Convierte los datos a formato JSON y reemplaza el ObjectId por strings
for record in data:
    record["_id"] = str(record["_id"])

# Normaliza los datos directamente en un DataFrame
df = pd.json_normalize(data)

# Opcional: Muestra una vista previa del DataFrame
print(df.head())

# main.py

from log_processor import filtrar_logs_y_extraer_variables,buscar_llamada

def buscar_caso(ruta_archivo, numero_caso):
    resultado = filtrar_logs_y_extraer_variables(ruta_archivo, numero_caso)
    #print(resultado)
    return resultado

# Función principal

df['calificacion'] = 'FALSO'
df['realizada'] = 'FALSO'

ruta_archivo = '/tmp/full_log'
# Cambia esto a la ruta real del archivo

import json
import pandas as pd

# Función para filtrar logs y extraer variables
def filtrar_logs_y_extraer_variables(logs, numero_caso):
    for log in logs:
        if log.get('numero_caso') == str(numero_caso):
            calificacion = log.get('calificacion')
            return numero_caso, log.get('accion'), calificacion
    return None

# Función para buscar llamadas si no hay resultados directos
def buscar_llamada(logs, numero_caso):
    for log in logs:
        if log.get('numero_caso') == str(numero_caso):
            return log
    return None

# Cargar logs desde un archivo JSON
ruta_archivo_logs ="/tmp/resultados.json"


from analizador import AnalizadorLlamadas

analizador = AnalizadorLlamadas("/tmp/full_log")




with open(ruta_archivo_logs, "r") as archivo:
    logs = [json.loads(line) for line in archivo]

# Procesar cada fila del DataFrame
for index, row in df.iterrows():
    numero_caso = row["caso"]
    resultado = filtrar_logs_y_extraer_variables(logs, numero_caso)
    resultad = analizador.analizar_llamada(numero_caso)
    print(resultad['estado'])
    if resultad['estado']=="Completado":
     df.loc[index, "realizada"] = "VERDADERO"
    if resultado is not None:
        _, _, nueva_calificacion = resultado  # Extraer nueva calificación
        if int(nueva_calificacion) > 5:
         nueva_calificacion = 5
        df.loc[index, "calificacion"] = nueva_calificacion
        df.loc[index, "realizada"] = "VERDADERO"
        df.loc[index, "finalizo"] = "VERDADERO"
        print(f"Caso: {numero_caso}, Calificación actualizada a: {nueva_calificacion}")
    else:
        resultado = buscar_llamada(logs, numero_caso)
        if resultado is not None:
            df.loc[index, "realizada"] = "VERDADERO"
        print(f"No se encontraron resultados para el caso: {numero_caso}")

# Mostrar el DataFrame actualizado
print(df)


print("Archivo Excel creado exitosamente sin duplicados.")

print(df['calificacion'])

column_order = ['_id', 'telefono', 'descripcion', 'caso_db', 'nombre', 'apellido', 'realizada', 'calificacion', 'finalizo', 'reabrir', 'fecha', 'caso','asignado','grupo']
df = df[column_order]
# Eliminar duplicados
df_sin_duplicados = df.drop_duplicates(subset=['caso'])
df_sin_duplicados = df_sin_duplicados.applymap(lambda x: x.replace('true', 'VERDADERO') if isinstance(x, str) else x)
df_sin_duplicados = df_sin_duplicados.applymap(lambda x: x.replace('false', 'FALSO') if isinstance(x, str) else x)

# Guardar el DataFrame en un archivo Excel
df_sin_duplicados.to_excel(dat+'.xlsx', index=False)

print("Archivo Excel creado exitosamente sin duplicados.")



from email_class import Mail
"""
mail = Mail()
mail.send(
    to='<EMAIL>',
    subject='test',
    message='test',
    files_data=[('test.txt')],
)
"""


import base64

def encode_file_to_base64(file_path):
    """Convierte un archivo a base64 y devuelve un tuple con el nombre y el contenido codificado."""
    with open(file_path, "rb") as file:
        encoded_content = base64.b64encode(file.read()).decode("utf-8")
    return (file_path.split("/")[-1], encoded_content)

# Lista de rutas de archivos a adjuntar
file_paths = ["/home/<USER>/reporte/"+dat+'.xlsx']

# Convertimos los archivos antes de pasarlos a la clase
files_data = [encode_file_to_base64(path) for path in file_paths]

# Simulación de llamada a la clase con los archivos convertidos
mi_clase = Mail()  # Supongamos que es la clase que maneja el envío de correos
mi_clase.send(
    #to="<EMAIL>,<EMAIL>,<EMAIL>",
    to="<EMAIL>",
    subject="Reporte "+dat,
    message="Reporte Voicebot",
    files_data=files_data  # Aquí pasamos los archivos ya convertidos
)
