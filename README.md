# Sistema de Reportes de Voicebot

Sistema automatizado para procesar logs de Asterisk, analizar llamadas de voicebot y generar reportes en Excel con envío automático por email.

## Características

- ✅ **Sin Logstash**: Procesamiento nativo de logs en Python
- 📊 **Reportes Excel**: Generación automática con estadísticas
- 📧 **Envío por Email**: Distribución automática de reportes
- 🐳 **Integración Docker**: Extracción automática de logs de Asterisk
- 🗄️ **MongoDB**: Consulta de datos de casos
- 📈 **Análisis de Llamadas**: Estado y calificaciones de llamadas

## Estructura del Proyecto

```
voicebot_reporter/
├── config/                 # Configuración del sistema
│   ├── settings.py         # Configuraciones principales
│   └── __init__.py
├── src/                    # Código fuente
│   ├── log_processor/      # Procesamiento de logs
│   │   ├── asterisk_parser.py
│   │   ├── call_analyzer.py
│   │   └── __init__.py
│   ├── database/           # Manejo de base de datos
│   │   ├── mongo_client.py
│   │   └── __init__.py
│   ├── reports/            # Generación de reportes
│   │   ├── excel_generator.py
│   │   ├── email_sender.py
│   │   └── __init__.py
│   └── utils/              # Utilidades
│       ├── docker_utils.py
│       ├── file_utils.py
│       └── __init__.py
├── main.py                 # Script principal
├── requirements.txt        # Dependencias
├── .env.example           # Ejemplo de configuración
└── README.md              # Este archivo
```

## Instalación

1. **Clonar o descargar el proyecto**

2. **Instalar dependencias**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configurar variables de entorno**:
   ```bash
   cp .env.example .env
   # Editar .env con tus configuraciones
   ```

4. **Configurar email**:
   - Para Gmail, usar contraseña de aplicación
   - Habilitar autenticación de 2 factores
   - Generar contraseña de aplicación específica

## Configuración

### Email
Edita el archivo `.env`:
```env
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=tu_contraseña_de_aplicacion
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
```

### MongoDB
Las configuraciones están en `config/settings.py`. Puedes sobrescribirlas en `.env`:
```env
MONGODB_HOST=***********
MONGODB_PORT=27017
MONGODB_USERNAME=admin
MONGODB_PASSWORD=V0iC3boatT
```

## Uso

### Proceso Completo
```bash
# Generar reporte para hoy
python main.py

# Generar reporte para fecha específica
python main.py --fecha 2025-01-01

# Sin copiar logs de Docker
python main.py --no-copy-logs

# Sin enviar email
python main.py --no-email
```

### Opciones Específicas
```bash
# Solo estadísticas
python main.py --stats-only

# Probar configuración de email
python main.py --test-email

# Ayuda
python main.py --help
```

## Funcionalidades

### 1. Procesamiento de Logs
- Extrae logs de contenedor Docker de Asterisk
- Procesa logs sin necesidad de Logstash
- Identifica llamadas y calificaciones
- Genera archivo JSON con resultados

### 2. Análisis de Llamadas
- Determina estado de llamadas (Completado/Iniciado/No encontrado)
- Extrae calificaciones de llamadas
- Genera estadísticas de rendimiento

### 3. Generación de Reportes
- Crea archivos Excel con datos consolidados
- Incluye hoja de estadísticas
- Elimina duplicados automáticamente
- Normaliza valores booleanos

### 4. Envío por Email
- Envío automático de reportes
- Soporte para múltiples destinatarios
- Archivos adjuntos en base64
- Configuración flexible de SMTP

## Migración desde Logstash

Este sistema reemplaza completamente Logstash:

### Antes (con Logstash):
1. Logstash procesaba logs → JSON
2. Script Python leía JSON
3. Generaba reporte

### Ahora (sin Logstash):
1. Python procesa logs directamente
2. Genera JSON internamente
3. Crea reporte en un solo proceso

## Archivos de Salida

- **Excel**: `{fecha}.xlsx` en directorio configurado
- **JSON**: `/tmp/resultados.json` (temporal)
- **Logs**: `/tmp/full_log` (copiado desde Docker)

## Solución de Problemas

### Error de conexión a MongoDB
```bash
# Verificar conectividad
ping ***********

# Verificar credenciales en config/settings.py
```

### Error de Docker
```bash
# Verificar contenedor
docker ps | grep asterisk

# Verificar logs manualmente
docker cp asterisk:/var/log/asterisk/full /tmp/full_log
```

### Error de Email
```bash
# Probar configuración
python main.py --test-email

# Verificar credenciales en .env
```

## Desarrollo

### Estructura de Clases Principales

- `VoicebotReporter`: Orquestador principal
- `AsteriskLogParser`: Procesamiento de logs
- `AnalizadorLlamadas`: Análisis de estado de llamadas
- `MongoDBClient`: Cliente de base de datos
- `ExcelReportGenerator`: Generación de reportes
- `EmailSender`: Envío de emails

### Agregar Nuevas Funcionalidades

1. **Nuevos procesadores**: Agregar en `src/log_processor/`
2. **Nuevos reportes**: Agregar en `src/reports/`
3. **Nuevas utilidades**: Agregar en `src/utils/`
4. **Configuraciones**: Modificar `config/settings.py`

## Licencia

Proyecto interno - Todos los derechos reservados.
