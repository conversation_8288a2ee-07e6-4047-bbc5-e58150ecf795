#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Prueba del endpoint de email
"""
import sys
import json
import requests
import time
import threading

sys.path.append('.')

def start_api_server():
    """Inicia el servidor API en background"""
    try:
        from api_flask import app
        app.run(host="127.0.0.1", port=5000, debug=False)
    except Exception as e:
        print(f"Error iniciando servidor: {e}")

def test_email_endpoint():
    """Prueba el endpoint de email"""
    print("=== PRUEBA DEL ENDPOINT DE EMAIL ===\n")
    
    # Iniciar servidor en background
    print("Iniciando servidor API...")
    server_thread = threading.Thread(target=start_api_server, daemon=True)
    server_thread.start()
    
    # Esperar a que el servidor inicie
    time.sleep(3)
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        # 1. Probar health check
        print("1. Probando health check...")
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("   ✅ Health check: OK")
        else:
            print(f"   ❌ Health check falló: {response.status_code}")
            return False
        
        # 2. Probar endpoint raíz
        print("2. Probando endpoint raíz...")
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ API Info: {data['message']}")
        else:
            print(f"   ❌ Endpoint raíz falló: {response.status_code}")
        
        # 3. Probar envío de email (sin reporte)
        print("3. Probando envío de email...")
        email_data = {
            "to": "<EMAIL>",
            "subject": "Prueba Sistema Voicebot",
            "message": "Este es un email de prueba del sistema reorganizado sin Logstash.",
            "include_report": False
        }
        
        response = requests.post(f"{base_url}/send-email", json=email_data, timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Email programado: {data['message']}")
        else:
            print(f"   ❌ Error enviando email: {response.status_code}")
            print(f"       Respuesta: {response.text}")
        
        # 4. Probar estadísticas
        print("4. Probando estadísticas...")
        response = requests.get(f"{base_url}/statistics", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Estadísticas obtenidas para fecha: {data['fecha']}")
            if 'estadisticas' in data:
                stats = data['estadisticas']
                print(f"       Total casos: {stats.get('total_casos', 'N/A')}")
        else:
            print(f"   ❌ Error obteniendo estadísticas: {response.status_code}")
        
        print("\n🎉 ENDPOINTS FUNCIONANDO CORRECTAMENTE!")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ No se pudo conectar al servidor API")
        print("   Asegúrate de que no hay otro proceso usando el puerto 5000")
        return False
    except Exception as e:
        print(f"❌ Error probando endpoints: {e}")
        return False

def show_api_usage():
    """Muestra ejemplos de uso de la API"""
    print("\n=== EJEMPLOS DE USO DE LA API ===")
    
    examples = [
        {
            "title": "Enviar email simple",
            "method": "POST",
            "url": "http://localhost:5000/send-email",
            "data": {
                "to": "<EMAIL>",
                "subject": "Reporte Voicebot",
                "message": "Adjunto el reporte solicitado",
                "include_report": False
            }
        },
        {
            "title": "Generar reporte completo",
            "method": "POST", 
            "url": "http://localhost:5000/generate-report",
            "data": {
                "fecha": "2025-01-01",
                "send_email": True,
                "copy_logs": True
            }
        },
        {
            "title": "Obtener estadísticas",
            "method": "GET",
            "url": "http://localhost:5000/statistics?fecha=2025-01-01",
            "data": None
        }
    ]
    
    for example in examples:
        print(f"\n{example['title']}:")
        print(f"  {example['method']} {example['url']}")
        if example['data']:
            print(f"  Body: {json.dumps(example['data'], indent=2)}")
    
    print(f"\n📚 Documentación completa disponible en:")
    print(f"   - FastAPI: http://localhost:8000/docs")
    print(f"   - Archivo: API_DOCUMENTATION.md")

def main():
    print("=" * 60)
    print("PRUEBA DE ENDPOINTS DE EMAIL")
    print("=" * 60)
    
    success = test_email_endpoint()
    
    if success:
        show_api_usage()
    
    print(f"\n" + "=" * 60)
    if success:
        print("🎉 SISTEMA DE ENDPOINTS FUNCIONANDO!")
        print("   ✅ API REST disponible")
        print("   ✅ Endpoints de email operativos")
        print("   ✅ Sistema listo para producción")
    else:
        print("❌ PROBLEMAS CON LOS ENDPOINTS")
        print("   Revisar configuración del servidor")
    print("=" * 60)

if __name__ == "__main__":
    main()
