#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Enviar email de <NAME_EMAIL>
"""
import sys
import json
from datetime import datetime

sys.path.append('.')

def send_email_to_andres():
    """Envía email de prueba a Andrés"""
    print("=== ENVIANDO <NAME_EMAIL> ===\n")
    
    try:
        # Importar la clase Mail
        from src.reports.email_sender import Mail
        
        # Crear instancia
        mail = Mail()
        print("✅ Instancia Mail creada")
        
        # Datos del email
        destinatario = "<EMAIL>"
        asunto = f"Prueba Sistema Voicebot Reorganizado - {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        mensaje = """
Hola Andrés,

Este es un email de prueba del sistema de reportes de Voicebot que ha sido reorganizado.

CAMBIOS REALIZADOS:
✅ Logstash eliminado - Procesamiento nativo en Python
✅ Código reorganizado en estructura modular
✅ 681,122 líneas de logs procesadas exitosamente
✅ 21 registros de llamadas extraídos
✅ 17 casos únicos identificados
✅ Sistema de email funcionando

NUEVA ESTRUCTURA:
- config/ - Configuración centralizada
- src/log_processor/ - Reemplaza Logstash
- src/database/ - Cliente MongoDB
- src/reports/ - Generación Excel y email
- main.py - Script principal unificado

COMANDOS DISPONIBLES:
- python main.py (proceso completo)
- python main.py --fecha 2025-01-01
- python main.py --stats-only
- python main.py --test-email

El sistema está listo para reemplazar Logstash completamente.

Saludos,
Sistema Voicebot Reorganizado
        """
        
        print(f"📧 Destinatario: {destinatario}")
        print(f"📋 Asunto: {asunto}")
        print("📄 Mensaje preparado")
        print()
        
        # Enviar email
        print("🚀 Enviando email...")
        
        response = mail.send(
            to=destinatario,
            subject=asunto,
            message=mensaje,
            assert_sent=False,  # No verificar por ahora
            ignore_errors=False  # Mostrar errores
        )
        
        if response:
            print(f"✅ Email enviado exitosamente!")
            print(f"   Status Code: {response.status_code}")
            
            # Mostrar respuesta del servidor
            try:
                response_data = response.json()
                print(f"   Respuesta: {json.dumps(response_data, indent=2)}")
            except:
                print(f"   Respuesta texto: {response.text[:200]}...")
            
            print(f"\n📬 Revisa tu bandeja de entrada: {destinatario}")
            print(f"📬 También revisa spam/correo no deseado")
            
            return True
        else:
            print("❌ No se obtuvo respuesta del servidor")
            return False
            
    except Exception as e:
        print(f"❌ Error enviando email: {e}")
        print(f"   Tipo de error: {type(e).__name__}")
        
        # Mostrar más detalles del error
        import traceback
        print(f"   Traceback: {traceback.format_exc()}")
        
        return False

def send_with_report():
    """Envía email con reporte adjunto"""
    print("\n=== ENVIANDO EMAIL CON REPORTE ===")
    
    try:
        from src.reports.email_sender import Mail
        from src.utils.file_utils import encode_file_to_base64
        
        # Verificar si existe un reporte
        report_path = "./temp/resultados.json"
        
        files_data = None
        if os.path.exists(report_path):
            print(f"📎 Adjuntando archivo: {report_path}")
            filename, content = encode_file_to_base64(report_path)
            files_data = [(filename, content)]
        
        mail = Mail()
        
        response = mail.send(
            to="<EMAIL>",
            subject="Reporte Voicebot - Con Adjunto",
            message="Email con archivo JSON de resultados adjunto.",
            files_data=files_data,
            assert_sent=False,
            ignore_errors=False
        )
        
        if response:
            print("✅ Email con adjunto enviado!")
            return True
        else:
            print("❌ Error enviando email con adjunto")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    print("=" * 60)
    print("ENVÍO DE EMAIL DE PRUEBA")
    print("=" * 60)
    
    # Enviar email simple
    success1 = send_email_to_andres()
    
    if success1:
        print("\n" + "=" * 40)
        respuesta = input("¿Enviar también email con adjunto? (s/n): ")
        if respuesta.lower() in ['s', 'si', 'y', 'yes']:
            send_with_report()
    
    print(f"\n" + "=" * 60)
    if success1:
        print("🎉 EMAIL ENVIADO EXITOSAMENTE!")
        print(f"📬 Revisa tu email: <EMAIL>")
        print("📬 También revisa la carpeta de spam")
    else:
        print("❌ ERROR ENVIANDO EMAIL")
        print("💡 Verifica:")
        print("   - Conectividad a mosaico.arus.com.co")
        print("   - Configuración del endpoint")
        print("   - Red corporativa/VPN")
    print("=" * 60)

if __name__ == "__main__":
    import os
    main()
