# -*- coding: utf-8 -*-
"""
Generador de reportes Excel
"""
import pandas as pd
import os
from typing import Dict, List, Optional
from config.settings import REPORT_CONFIG, FILE_CONFIG, get_current_date
from src.utils.file_utils import ensure_directory_exists


class ExcelReportGenerator:
    """Clase para generar reportes en formato Excel"""
    
    def __init__(self, output_directory: str = None):
        self.output_directory = output_directory or FILE_CONFIG['output_directory']
        ensure_directory_exists(self.output_directory)
    
    def prepare_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Prepara el DataFrame para el reporte aplicando transformaciones necesarias
        
        Args:
            df: DataFrame original
            
        Returns:
            DataFrame preparado
        """
        # Crear una copia para no modificar el original
        df_prepared = df.copy()
        
        # Asegurar que las columnas necesarias existen
        required_columns = {
            'calificacion': 'FALSO',
            'realizada': 'FALSO',
            'finalizo': 'FALSO'
        }
        
        for col, default_value in required_columns.items():
            if col not in df_prepared.columns:
                df_prepared[col] = default_value
        
        # Reordenar columnas según configuración
        available_columns = [col for col in REPORT_CONFIG['excel_columns'] if col in df_prepared.columns]
        df_prepared = df_prepared[available_columns]
        
        # Eliminar duplicados basado en la columna 'caso'
        if 'caso' in df_prepared.columns:
            df_prepared = df_prepared.drop_duplicates(subset=['caso'])
            print(f"Duplicados eliminados. Filas restantes: {len(df_prepared)}")
        
        # Normalizar valores booleanos
        df_prepared = df_prepared.applymap(self._normalize_boolean_values)
        
        return df_prepared
    
    def _normalize_boolean_values(self, value):
        """Normaliza valores booleanos a formato español"""
        if isinstance(value, str):
            if value.lower() == 'true':
                return 'VERDADERO'
            elif value.lower() == 'false':
                return 'FALSO'
        return value
    
    def update_call_results(self, df: pd.DataFrame, call_results: Dict[str, Dict]) -> pd.DataFrame:
        """
        Actualiza el DataFrame con los resultados de las llamadas
        
        Args:
            df: DataFrame original
            call_results: Diccionario con resultados de llamadas
            
        Returns:
            DataFrame actualizado
        """
        df_updated = df.copy()
        
        for index, row in df_updated.iterrows():
            if 'caso' not in row:
                continue
                
            numero_caso = str(row['caso'])
            
            if numero_caso in call_results:
                result = call_results[numero_caso]
                
                # Actualizar estado de la llamada
                if result['realizada']:
                    df_updated.loc[index, 'realizada'] = 'VERDADERO'
                
                # Actualizar calificación si existe
                if result['calificacion']:
                    calificacion = int(result['calificacion'])
                    # Aplicar límite máximo de calificación
                    if calificacion > REPORT_CONFIG['max_calificacion']:
                        calificacion = REPORT_CONFIG['max_calificacion']
                    
                    df_updated.loc[index, 'calificacion'] = str(calificacion)
                    df_updated.loc[index, 'finalizo'] = 'VERDADERO'
                
                print(f"Caso {numero_caso} actualizado: {result['estado']}")
        
        return df_updated
    
    def generate_excel_report(
        self, 
        df: pd.DataFrame, 
        filename: str = None,
        include_statistics: bool = True
    ) -> str:
        """
        Genera un reporte Excel
        
        Args:
            df: DataFrame con los datos
            filename: Nombre del archivo (opcional)
            include_statistics: Si incluir hoja de estadísticas
            
        Returns:
            Ruta del archivo generado
        """
        if filename is None:
            date_str = get_current_date()
            filename = f"{date_str}.xlsx"
        
        # Asegurar extensión .xlsx
        if not filename.endswith('.xlsx'):
            filename += '.xlsx'
        
        file_path = os.path.join(self.output_directory, filename)
        
        try:
            # Preparar DataFrame
            df_prepared = self.prepare_dataframe(df)
            
            # Crear archivo Excel con múltiples hojas
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                # Hoja principal con datos
                df_prepared.to_excel(writer, sheet_name='Reporte', index=False)
                
                # Hoja de estadísticas si se solicita
                if include_statistics:
                    stats_df = self._generate_statistics(df_prepared)
                    stats_df.to_excel(writer, sheet_name='Estadísticas', index=False)
            
            print(f"Reporte Excel generado exitosamente: {file_path}")
            print(f"Total de registros: {len(df_prepared)}")
            
            return file_path
            
        except Exception as e:
            print(f"Error generando reporte Excel: {e}")
            raise
    
    def _generate_statistics(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Genera estadísticas del reporte
        
        Args:
            df: DataFrame con los datos
            
        Returns:
            DataFrame con estadísticas
        """
        stats = []
        
        # Estadísticas básicas
        total_casos = len(df)
        stats.append(['Total de casos', total_casos])
        
        # Llamadas realizadas
        if 'realizada' in df.columns:
            realizadas = len(df[df['realizada'] == 'VERDADERO'])
            stats.append(['Llamadas realizadas', realizadas])
            stats.append(['Porcentaje realizadas', f"{(realizadas/total_casos*100):.1f}%" if total_casos > 0 else "0%"])
        
        # Llamadas finalizadas
        if 'finalizo' in df.columns:
            finalizadas = len(df[df['finalizo'] == 'VERDADERO'])
            stats.append(['Llamadas finalizadas', finalizadas])
            stats.append(['Porcentaje finalizadas', f"{(finalizadas/total_casos*100):.1f}%" if total_casos > 0 else "0%"])
        
        # Estadísticas de calificaciones
        if 'calificacion' in df.columns:
            # Filtrar calificaciones válidas (no 'FALSO')
            calificaciones_validas = df[df['calificacion'] != 'FALSO']['calificacion']
            if len(calificaciones_validas) > 0:
                try:
                    calificaciones_numericas = pd.to_numeric(calificaciones_validas, errors='coerce').dropna()
                    if len(calificaciones_numericas) > 0:
                        stats.append(['Calificación promedio', f"{calificaciones_numericas.mean():.2f}"])
                        stats.append(['Calificación máxima', int(calificaciones_numericas.max())])
                        stats.append(['Calificación mínima', int(calificaciones_numericas.min())])
                        
                        # Distribución de calificaciones
                        for i in range(1, 6):
                            count = len(calificaciones_numericas[calificaciones_numericas == i])
                            stats.append([f'Calificación {i}', count])
                except Exception as e:
                    print(f"Error calculando estadísticas de calificaciones: {e}")
        
        return pd.DataFrame(stats, columns=['Métrica', 'Valor'])
    
    def generate_summary_report(self, multiple_dataframes: Dict[str, pd.DataFrame]) -> str:
        """
        Genera un reporte resumen con múltiples fechas
        
        Args:
            multiple_dataframes: Diccionario {fecha: dataframe}
            
        Returns:
            Ruta del archivo generado
        """
        filename = f"resumen_{get_current_date()}.xlsx"
        file_path = os.path.join(self.output_directory, filename)
        
        try:
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                # Crear hoja resumen
                summary_stats = []
                
                for fecha, df in multiple_dataframes.items():
                    df_prepared = self.prepare_dataframe(df)
                    total = len(df_prepared)
                    realizadas = len(df_prepared[df_prepared['realizada'] == 'VERDADERO']) if 'realizada' in df_prepared.columns else 0
                    
                    summary_stats.append([fecha, total, realizadas, f"{(realizadas/total*100):.1f}%" if total > 0 else "0%"])
                    
                    # Agregar cada fecha como hoja separada
                    sheet_name = fecha.replace('-', '_')
                    df_prepared.to_excel(writer, sheet_name=sheet_name, index=False)
                
                # Crear hoja resumen
                summary_df = pd.DataFrame(summary_stats, columns=['Fecha', 'Total Casos', 'Realizadas', 'Porcentaje'])
                summary_df.to_excel(writer, sheet_name='Resumen', index=False)
            
            print(f"Reporte resumen generado: {file_path}")
            return file_path
            
        except Exception as e:
            print(f"Error generando reporte resumen: {e}")
            raise
