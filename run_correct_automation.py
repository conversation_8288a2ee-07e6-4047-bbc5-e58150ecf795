#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Automatización corregida siguiendo exactamente el flujo de py.py
"""
import pandas as pd
import json
import pymongo
import subprocess
import base64
import os
from datetime import datetime

def copy_docker_logs():
    """Copia logs de Docker como en el original"""
    print("🐳 Copiando logs de Docker...")
    try:
        subprocess.run(["docker", "cp", "asterisk:/var/log/asterisk/full", "/tmp/full_log"], check=True)
        print("✅ Docker copy command executed successfully.")
        return True
    except subprocess.CalledProcessError as e:
        print(f"⚠️  Error executing Docker command: {e}")
        print("   Usando archivo local: ./full_log")
        return False

def get_mongodb_data(fecha):
    """Obtiene datos de MongoDB como en el original"""
    print(f"🗄️  Conectando a MongoDB para fecha: {fecha}")
    
    try:
        # Conexión exacta como en py.py
        client = pymongo.MongoClient("********************************************/")
        db = client['mydatabase']
        collection = db[fecha]
        
        # Encuentra todos los documentos
        data = list(collection.find({}))
        
        if not data:
            print(f"❌ No se encontraron datos para la fecha {fecha}")
            return None
        
        # Convierte ObjectId a string
        for record in data:
            record["_id"] = str(record["_id"])
        
        # Normaliza a DataFrame
        df = pd.json_normalize(data)
        
        print(f"✅ Obtenidos {len(df)} registros de MongoDB")
        print(f"   Columnas: {list(df.columns)}")
        
        return df
        
    except Exception as e:
        print(f"❌ Error conectando a MongoDB: {e}")
        return None

def process_logs_to_json():
    """Procesa logs y genera JSON como haría Logstash"""
    print("📊 Procesando logs (reemplazando Logstash)...")
    
    try:
        import sys
        sys.path.append('.')
        from src.log_processor.asterisk_parser import AsteriskLogParser
        
        # Usar archivo local si no se pudo copiar de Docker
        log_path = "/tmp/full_log" if os.path.exists("/tmp/full_log") else "./full_log"
        
        parser = AsteriskLogParser(log_path)
        success = parser.guardar_resultados_json("/tmp/resultados.json")
        
        if success:
            print("✅ Logs procesados y JSON generado")
            return True
        else:
            print("❌ Error procesando logs")
            return False
            
    except Exception as e:
        print(f"❌ Error en procesamiento: {e}")
        return False

def filtrar_logs_y_extraer_variables(logs, numero_caso):
    """Función exacta del py.py original"""
    for log in logs:
        if log.get('numero_caso') == str(numero_caso):
            calificacion = log.get('calificacion')
            return numero_caso, log.get('accion'), calificacion
    return None

def buscar_llamada(logs, numero_caso):
    """Función exacta del py.py original"""
    for log in logs:
        if log.get('numero_caso') == str(numero_caso):
            return log
    return None

def process_dataframe_with_logs(df):
    """Procesa DataFrame con logs siguiendo exactamente py.py"""
    print("🔄 Procesando DataFrame con logs...")
    
    try:
        # Inicializar columnas como en py.py
        df['calificacion'] = 'FALSO'
        df['realizada'] = 'FALSO'
        df['finalizo'] = 'FALSO'  # Agregar esta columna que falta
        
        # Cargar logs desde JSON
        ruta_archivo_logs = "/tmp/resultados.json"
        
        if not os.path.exists(ruta_archivo_logs):
            print(f"❌ Archivo de logs no encontrado: {ruta_archivo_logs}")
            return None
        
        with open(ruta_archivo_logs, "r", encoding='utf-8') as archivo:
            logs = [json.loads(line) for line in archivo if line.strip()]
        
        print(f"📋 Cargados {len(logs)} registros de logs")
        
        # Crear analizador como en py.py
        import sys
        sys.path.append('.')
        from src.log_processor.call_analyzer import AnalizadorLlamadas
        
        log_path = "/tmp/full_log" if os.path.exists("/tmp/full_log") else "./full_log"
        analizador = AnalizadorLlamadas(log_path)
        
        # Procesar cada fila EXACTAMENTE como en py.py
        casos_procesados = 0
        casos_encontrados = 0
        
        for index, row in df.iterrows():
            numero_caso = row["caso"]
            
            # Analizar llamada
            resultad = analizador.analizar_llamada(numero_caso)
            print(f"Caso {numero_caso}: {resultad['estado']}")
            
            if resultad['estado'] == "Completado":
                df.loc[index, "realizada"] = "VERDADERO"
            
            # Buscar en logs procesados
            resultado = filtrar_logs_y_extraer_variables(logs, numero_caso)
            
            if resultado is not None:
                _, _, nueva_calificacion = resultado
                
                # Aplicar límite de calificación como en py.py
                if nueva_calificacion and nueva_calificacion.strip():
                    try:
                        cal_int = int(nueva_calificacion)
                        if cal_int > 5:
                            cal_int = 5
                        df.loc[index, "calificacion"] = str(cal_int)
                        df.loc[index, "realizada"] = "VERDADERO"
                        df.loc[index, "finalizo"] = "VERDADERO"
                        casos_encontrados += 1
                        print(f"  ✅ Calificación actualizada a: {cal_int}")
                    except ValueError:
                        print(f"  ⚠️  Calificación inválida: {nueva_calificacion}")
            else:
                # Buscar llamada sin calificación
                resultado = buscar_llamada(logs, numero_caso)
                if resultado is not None:
                    df.loc[index, "realizada"] = "VERDADERO"
                    print(f"  📞 Llamada encontrada sin calificación")
                else:
                    print(f"  ❌ No se encontraron resultados")
            
            casos_procesados += 1
        
        print(f"✅ Procesados {casos_procesados} casos, {casos_encontrados} con calificación")
        return df
        
    except Exception as e:
        print(f"❌ Error procesando DataFrame: {e}")
        import traceback
        print(traceback.format_exc())
        return None

def create_excel_report(df, fecha):
    """Crea reporte Excel siguiendo exactamente py.py"""
    print("📊 Creando reporte Excel...")
    
    try:
        # Orden de columnas EXACTO de py.py
        column_order = [
            '_id', 'telefono', 'descripcion', 'caso_db', 'nombre', 
            'apellido', 'realizada', 'calificacion', 'finalizo', 
            'reabrir', 'fecha', 'caso', 'asignado', 'grupo'
        ]
        
        # Verificar qué columnas existen
        existing_columns = [col for col in column_order if col in df.columns]
        missing_columns = [col for col in column_order if col not in df.columns]
        
        if missing_columns:
            print(f"⚠️  Columnas faltantes: {missing_columns}")
            # Agregar columnas faltantes con valores por defecto
            for col in missing_columns:
                df[col] = ''
        
        # Reordenar columnas
        df = df[column_order]
        
        # Eliminar duplicados por 'caso' como en py.py
        df_sin_duplicados = df.drop_duplicates(subset=['caso'])
        
        # Normalizar valores booleanos EXACTO como py.py
        df_sin_duplicados = df_sin_duplicados.applymap(
            lambda x: x.replace('true', 'VERDADERO') if isinstance(x, str) else x
        )
        df_sin_duplicados = df_sin_duplicados.applymap(
            lambda x: x.replace('false', 'FALSO') if isinstance(x, str) else x
        )
        
        # Crear directorio si no existe
        output_dir = "/home/<USER>/reporte/"
        if not os.path.exists(output_dir):
            output_dir = "./reports/"
            os.makedirs(output_dir, exist_ok=True)
        
        # Guardar Excel con nombre exacto como py.py
        excel_path = os.path.join(output_dir, f"{fecha}.xlsx")
        df_sin_duplicados.to_excel(excel_path, index=False)
        
        print(f"✅ Archivo Excel creado: {excel_path}")
        print(f"   Registros finales: {len(df_sin_duplicados)}")
        
        return excel_path
        
    except Exception as e:
        print(f"❌ Error creando Excel: {e}")
        return None

def send_email_report(excel_path, fecha):
    """Envía email EXACTO como py.py"""
    print("📧 Enviando reporte por email...")
    
    try:
        # Función encode exacta de py.py
        def encode_file_to_base64(file_path):
            with open(file_path, "rb") as file:
                encoded_content = base64.b64encode(file.read()).decode("utf-8")
            return (file_path.split("/")[-1], encoded_content)
        
        # Preparar archivos como en py.py
        file_paths = [excel_path]
        files_data = [encode_file_to_base64(path) for path in file_paths]
        
        # Importar Mail como en py.py
        import sys
        sys.path.append('.')
        from src.reports.email_sender import Mail
        
        # Enviar email EXACTO como py.py
        mi_clase = Mail()
        mi_clase.send(
            to="<EMAIL>",  # Usar Gmail que funciona
            subject=f"Reporte {fecha}",
            message="Reporte Voicebot",
            files_data=files_data
        )
        
        print("✅ Email enviado exitosamente")
        return True
        
    except Exception as e:
        print(f"❌ Error enviando email: {e}")
        return False

def main():
    """Función principal siguiendo exactamente py.py"""
    print("=" * 60)
    print("🤖 AUTOMATIZACIÓN CORREGIDA - SIGUIENDO PY.PY")
    print("=" * 60)
    
    # Fecha actual como en py.py
    fecha = datetime.now().strftime('%Y-%m-%d')
    print(f"📅 Fecha: {fecha}")
    
    try:
        # Paso 1: Copiar logs de Docker
        copy_docker_logs()
        
        # Paso 2: Obtener datos de MongoDB
        df = get_mongodb_data(fecha)
        if df is None:
            print("❌ No se pudieron obtener datos de MongoDB")
            return False
        
        # Paso 3: Procesar logs (reemplazar Logstash)
        if not process_logs_to_json():
            print("❌ Error procesando logs")
            return False
        
        # Paso 4: Procesar DataFrame con logs
        df_processed = process_dataframe_with_logs(df)
        if df_processed is None:
            print("❌ Error procesando DataFrame")
            return False
        
        # Paso 5: Crear Excel
        excel_path = create_excel_report(df_processed, fecha)
        if not excel_path:
            print("❌ Error creando Excel")
            return False
        
        # Paso 6: Enviar email
        if send_email_report(excel_path, fecha):
            print(f"\n🎉 AUTOMATIZACIÓN COMPLETADA!")
            print(f"📧 Revisa <EMAIL>")
            print(f"📊 Excel: {excel_path}")
            return True
        else:
            print("❌ Error enviando email")
            return False
            
    except Exception as e:
        print(f"❌ Error general: {e}")
        import traceback
        print(traceback.format_exc())
        return False

if __name__ == "__main__":
    main()
