# Sistema Voicebot Optimizado

Sistema automatizado para procesamiento de logs de Asterisk y generación de reportes, **sin dependencias de Logstash**.

## 🚀 Características

- ✅ **Sin Logstash**: Procesamiento nativo en Python
- ✅ **Optimizado**: Solo archivos esenciales
- ✅ **Automatizado**: Proceso completo end-to-end
- ✅ **Email automático**: Enví<NAME_EMAIL>
- ✅ **MongoDB**: Conexión directa a base de datos
- ✅ **Excel**: Generación de reportes estructurados

## 📁 Estructura del Proyecto

```
voicebot_optimized/
├── config/
│   ├── __init__.py
│   └── settings.py          # Configuración centralizada
├── src/
│   ├── __init__.py
│   ├── log_processor/       # Reemplaza Logstash
│   │   ├── __init__.py
│   │   ├── asterisk_parser.py
│   │   └── call_analyzer.py
│   └── reports/             # Sistema de reportes
│       ├── __init__.py
│       └── email_sender.py
├── temp/                    # Archivos temporales
├── reports/                 # Reportes Excel generados
├── main.py                  # Script principal
├── requirements.txt         # Dependencias
└── README.md               # Este archivo
```

## 🛠️ Instalación

1. **Instalar dependencias**:
```bash
pip install -r requirements.txt
```

2. **Verificar archivo de logs**:
   - El sistema busca el archivo `full_log` en el directorio padre
   - Asegúrate de que existe: `../full_log`

## 🚀 Uso

### Ejecución Simple
```bash
python main.py
```

### Lo que hace el sistema:
1. 🗄️ **Conecta a MongoDB** (fecha: 2025-06-24)
2. 📊 **Procesa logs** sin Logstash
3. 🔄 **Analiza llamadas** y calificaciones
4. 📊 **Genera Excel** con datos consolidados
5. 📧 **Envía email** a <EMAIL>

## ⚙️ Configuración

### MongoDB
```python
# config/settings.py
MONGODB_CONFIG = {
    'connection_string': '********************************************/'
}
```

### Email
```python
# config/settings.py
EMAIL_CONFIG = {
    'default_recipient': '<EMAIL>'
}
```

### Archivos
```python
# config/settings.py
FILE_CONFIG = {
    'log_file_path': '../full_log',  # Archivo de logs de Asterisk
    'temp_dir': './temp',
    'reports_dir': './reports'
}
```

## 📊 Salida

### Consola
```
🤖 SISTEMA VOICEBOT OPTIMIZADO
📅 Fecha: 2025-06-24
🗄️ Conectando a MongoDB...
✅ Obtenidos 17 registros de MongoDB
📊 Procesando logs (reemplazando Logstash)...
✅ Logs procesados y JSON generado
🔄 Procesando DataFrame con logs...
✅ Procesados 17 casos, 5 con calificación
📊 Creando reporte Excel...
✅ Archivo Excel creado: ./reports/2025-06-24.xlsx
📧 Enviando reporte por email...
✅ Email enviado exitosamente

🎉 AUTOMATIZACIÓN COMPLETADA!
📧 Revisa <EMAIL>
📊 Excel: ./reports/2025-06-24.xlsx
```

### Archivos Generados
- `./reports/2025-06-24.xlsx` - Reporte Excel
- `./temp/resultados.json` - Datos procesados de logs

## 🔧 Dependencias Mínimas

- `pandas` - Manipulación de datos
- `pymongo` - Conexión MongoDB
- `requests` - Envío de emails
- `chardet` - Detección de codificación
- `openpyxl` - Generación Excel
- `urllib3` - Requests HTTP

## 📈 Ventajas vs Versión Anterior

| Aspecto | Anterior | Optimizado |
|---------|----------|------------|
| **Archivos** | 50+ archivos | 12 archivos esenciales |
| **Logstash** | ❌ Requerido | ✅ Eliminado |
| **Dependencias** | 20+ paquetes | 6 paquetes |
| **Configuración** | Múltiples archivos | 1 archivo centralizado |
| **Mantenimiento** | Complejo | Simple |

## 🚨 Notas Importantes

1. **Archivo de logs**: Debe existir `../full_log` con logs de Asterisk
2. **MongoDB**: Conexión directa a ***********:27017
3. **Email**: <NAME_EMAIL>
4. **Fecha**: Hardcodeada a 2025-06-24 (fecha con datos conocidos)

## 🐛 Solución de Problemas

### Error: Archivo de logs no encontrado
```bash
# Verificar que existe el archivo
ls -la ../full_log
```

### Error: MongoDB no conecta
```bash
# Verificar conectividad
ping ***********
```

### Error: Email no se envía
- Verificar conexión a internet
- Revisar logs de error en consola

## 📞 Soporte

Para problemas o mejoras, contactar: <EMAIL>
