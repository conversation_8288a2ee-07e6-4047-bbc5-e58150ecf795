#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para probar el procesamiento de logs sin Logstash
"""
import sys
import re
import json
from datetime import datetime

sys.path.append('.')
from config.settings import LOG_PATTERNS

def process_log_efficiently(log_file_path):
    """Procesa el log de manera eficiente, línea por línea"""
    print(f"Procesando archivo: {log_file_path}")
    
    # Patrón para encontrar líneas con reporte.py
    pattern_completo = LOG_PATTERNS['agi_pattern'].replace('{numero_caso}', r'([^\"]*)')
    print(f"Patrón usado: {pattern_completo}")
    
    resultados = []
    total_lines = 0
    matching_lines = 0
    
    try:
        with open(log_file_path, 'r', encoding='utf-8', errors='ignore') as f:
            for line_num, line in enumerate(f, 1):
                total_lines += 1
                
                # Solo procesar líneas que contengan reporte.py
                if 'reporte.py' in line:
                    match = re.search(pattern_completo, line)
                    if match:
                        matching_lines += 1
                        resultado = {
                            'numero_caso': match.group(1),
                            'accion': match.group(2),
                            'calificacion': match.group(3),
                            'line_number': line_num,
                            'timestamp': extract_timestamp(line)
                        }
                        resultados.append(resultado)
                        
                        # Mostrar progreso cada 10 matches
                        if matching_lines % 10 == 0:
                            print(f"  Procesados {matching_lines} registros...")
    
    except Exception as e:
        print(f"Error procesando archivo: {e}")
        return []
    
    print(f"\n=== RESUMEN ===")
    print(f"Total líneas procesadas: {total_lines:,}")
    print(f"Líneas con reporte.py: {matching_lines}")
    print(f"Registros válidos extraídos: {len(resultados)}")
    
    return resultados

def extract_timestamp(line):
    """Extrae timestamp de una línea de log"""
    timestamp_pattern = r'\[(.*?)\]'
    match = re.search(timestamp_pattern, line)
    return match.group(1) if match else None

def save_results_json(resultados, output_path):
    """Guarda resultados en formato JSON como lo haría Logstash"""
    try:
        # Crear directorio si no existe
        import os
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        with open(output_path, 'w', encoding='utf-8') as archivo:
            for resultado in resultados:
                json.dump(resultado, archivo, ensure_ascii=False)
                archivo.write('\n')

        print(f"Resultados guardados en: {output_path}")
        return True
    except Exception as e:
        print(f"Error guardando resultados: {e}")
        return False

def main():
    print("=== PROCESADOR DE LOGS SIN LOGSTASH ===\n")
    
    # Procesar el log
    resultados = process_log_efficiently('./full_log')
    
    if resultados:
        print(f"\n=== PRIMEROS 5 REGISTROS ===")
        for i, r in enumerate(resultados[:5]):
            print(f"{i+1}. Caso: {r['numero_caso']}, Acción: {r['accion']}, Calificación: {r['calificacion']}")
        
        print(f"\n=== ÚLTIMOS 5 REGISTROS ===")
        for i, r in enumerate(resultados[-5:]):
            print(f"{len(resultados)-4+i}. Caso: {r['numero_caso']}, Acción: {r['accion']}, Calificación: {r['calificacion']}")
        
        # Estadísticas
        print(f"\n=== ESTADÍSTICAS ===")
        casos_unicos = len(set(r['numero_caso'] for r in resultados))
        con_calificacion = len([r for r in resultados if r['calificacion']])
        sin_calificacion = len([r for r in resultados if not r['calificacion']])
        
        print(f"Casos únicos: {casos_unicos}")
        print(f"Con calificación: {con_calificacion}")
        print(f"Sin calificación: {sin_calificacion}")
        
        # Guardar en JSON (usar directorio actual en Windows)
        if save_results_json(resultados, './temp/resultados.json'):
            print(f"\n✅ Procesamiento completado exitosamente!")
            print(f"   Archivo JSON generado: /tmp/resultados.json")
            print(f"   Total registros: {len(resultados)}")
        else:
            print(f"\n❌ Error guardando archivo JSON")
    else:
        print("❌ No se encontraron registros válidos")

if __name__ == "__main__":
    main()
