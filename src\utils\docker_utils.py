# -*- coding: utf-8 -*-
"""
Utilidades para operaciones con Docker
"""
import subprocess
from config.settings import DOCKER_CONFIG


class DockerManager:
    """Clase para manejar operaciones con Docker"""
    
    @staticmethod
    def copy_asterisk_logs(
        container_name: str = None,
        source_path: str = None,
        destination_path: str = None
    ) -> bool:
        """
        Copia logs de Asterisk desde el contenedor Docker
        
        Args:
            container_name: Nombre del contenedor
            source_path: Ruta del archivo en el contenedor
            destination_path: Ruta de destino en el host
            
        Returns:
            True si la copia fue exitosa
        """
        container_name = container_name or DOCKER_CONFIG['container_name']
        source_path = source_path or DOCKER_CONFIG['log_source_path']
        destination_path = destination_path or DOCKER_CONFIG['log_destination_path']
        
        try:
            command = [
                "docker", "cp", 
                f"{container_name}:{source_path}", 
                destination_path
            ]
            
            print(f"Ejecutando: {' '.join(command)}")
            result = subprocess.run(command, check=True, capture_output=True, text=True)
            
            print("Logs de Asterisk copiados exitosamente")
            print(f"Desde: {container_name}:{source_path}")
            print(f"Hacia: {destination_path}")
            
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"Error ejecutando comando Docker: {e}")
            print(f"Salida del error: {e.stderr}")
            return False
        except Exception as e:
            print(f"Error inesperado copiando logs: {e}")
            return False
    
    @staticmethod
    def check_container_status(container_name: str = None) -> bool:
        """
        Verifica si un contenedor está ejecutándose
        
        Args:
            container_name: Nombre del contenedor
            
        Returns:
            True si el contenedor está ejecutándose
        """
        container_name = container_name or DOCKER_CONFIG['container_name']
        
        try:
            command = ["docker", "ps", "--filter", f"name={container_name}", "--format", "{{.Names}}"]
            result = subprocess.run(command, check=True, capture_output=True, text=True)
            
            running_containers = result.stdout.strip().split('\n')
            is_running = container_name in running_containers
            
            if is_running:
                print(f"Contenedor {container_name} está ejecutándose")
            else:
                print(f"Contenedor {container_name} NO está ejecutándose")
            
            return is_running
            
        except subprocess.CalledProcessError as e:
            print(f"Error verificando estado del contenedor: {e}")
            return False
        except Exception as e:
            print(f"Error inesperado verificando contenedor: {e}")
            return False
    
    @staticmethod
    def get_container_logs(container_name: str = None, lines: int = 100) -> str:
        """
        Obtiene los logs recientes de un contenedor
        
        Args:
            container_name: Nombre del contenedor
            lines: Número de líneas a obtener
            
        Returns:
            Logs del contenedor como string
        """
        container_name = container_name or DOCKER_CONFIG['container_name']
        
        try:
            command = ["docker", "logs", "--tail", str(lines), container_name]
            result = subprocess.run(command, check=True, capture_output=True, text=True)
            
            return result.stdout
            
        except subprocess.CalledProcessError as e:
            print(f"Error obteniendo logs del contenedor: {e}")
            return ""
        except Exception as e:
            print(f"Error inesperado obteniendo logs: {e}")
            return ""
