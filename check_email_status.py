#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Verificar estado del email y diagnosticar problemas
"""
import sys
import requests
import json
from datetime import datetime

sys.path.append('.')

def check_email_delivery():
    """Verifica el estado de entrega del email"""
    print("=== DIAGNÓSTICO DE ENTREGA DE EMAIL ===\n")
    
    # IDs de los emails enviados (del log anterior)
    message_ids = [
        "<EMAIL>",
        "<EMAIL>"
    ]
    
    print("📧 Message IDs enviados:")
    for i, msg_id in enumerate(message_ids, 1):
        print(f"   {i}. {msg_id}")
    
    print(f"\n⏰ Tiempo transcurrido: ~5-10 minutos")
    print(f"📬 Destinatario: <EMAIL>")
    
    return True

def test_simple_email():
    """Envía un email más simple para probar"""
    print("\n=== ENVIANDO EMAIL SIMPLE DE PRUEBA ===")
    
    try:
        from src.reports.email_sender import Mail
        
        mail = Mail()
        
        # Email muy simple
        response = mail.send(
            to="<EMAIL>",
            subject="TEST SIMPLE - Sistema Voicebot",
            message="Email de prueba simple. Si recibes esto, el sistema funciona.",
            assert_sent=False,
            ignore_errors=False
        )
        
        if response and response.status_code == 201:
            print("✅ Email simple enviado")
            
            # Extraer información de la respuesta
            try:
                data = response.json()
                print(f"   Message ID: {data.get('responsemailer', {}).get('messageId', 'N/A')}")
                print(f"   Estado: {data.get('state', 'N/A')}")
                
                # Parsear respuesta del mailer
                mailer_response = json.loads(data.get('responsemailer', '{}'))
                print(f"   Aceptados: {mailer_response.get('accepted', [])}")
                print(f"   Rechazados: {mailer_response.get('rejected', [])}")
                print(f"   Respuesta SMTP: {mailer_response.get('response', 'N/A')}")
                
            except Exception as e:
                print(f"   Error parseando respuesta: {e}")
            
            return True
        else:
            print(f"❌ Error: Status {response.status_code if response else 'No response'}")
            return False
            
    except Exception as e:
        print(f"❌ Error enviando email simple: {e}")
        return False

def check_email_config():
    """Verifica la configuración de email"""
    print("\n=== VERIFICANDO CONFIGURACIÓN ===")
    
    try:
        from config.settings import EMAIL_CONFIG
        
        print(f"📧 Destinatarios configurados:")
        for recipient in EMAIL_CONFIG['recipients']:
            print(f"   - {recipient}")
        
        print(f"📋 Template asunto: {EMAIL_CONFIG['subject_template']}")
        print(f"💬 Mensaje: {EMAIL_CONFIG['message']}")
        
        # Verificar si andres.velez está en la lista
        if "<EMAIL>" in EMAIL_CONFIG['recipients']:
            print("✅ <EMAIL> está en la lista de destinatarios")
        else:
            print("⚠️  <EMAIL> NO está en la lista de destinatarios")
            print("💡 Agregando a la configuración...")
            
            # Agregar a la lista
            EMAIL_CONFIG['recipients'].append("<EMAIL>")
            print("✅ Agregado a la lista")
        
        return True
        
    except Exception as e:
        print(f"❌ Error verificando configuración: {e}")
        return False

def show_troubleshooting_guide():
    """Muestra guía de solución de problemas"""
    print("\n=== GUÍA DE SOLUCIÓN DE PROBLEMAS ===")
    
    print("🔍 POSIBLES CAUSAS:")
    print("   1. 📧 Email en carpeta de SPAM/Correo no deseado")
    print("   2. ⏰ Demora en entrega (puede tardar hasta 15-30 min)")
    print("   3. 🚫 Filtros de email corporativo bloqueando")
    print("   4. 📬 Problema con el servidor de email de ARUS")
    print("   5. 🌐 Problema de conectividad de red")
    
    print("\n✅ PASOS A SEGUIR:")
    print("   1. Revisar carpeta de SPAM en Outlook/Gmail")
    print("   2. Buscar por asunto: 'Sistema Voicebot'")
    print("   3. Revisar reglas de filtrado de email")
    print("   4. Contactar IT de ARUS si persiste")
    print("   5. Verificar que mosaico.arus.com.co esté accesible")
    
    print("\n🔧 COMANDOS DE VERIFICACIÓN:")
    print("   - ping mosaico.arus.com.co")
    print("   - nslookup mosaico.arus.com.co")
    print("   - telnet mosaico.arus.com.co 3000")

def test_alternative_recipient():
    """Prueba con otro destinatario si está disponible"""
    print("\n=== PRUEBA CON DESTINATARIO ALTERNATIVO ===")
    
    # Otros emails de la configuración original
    alternative_emails = [
        "<EMAIL>",
        "<EMAIL>"
    ]
    
    print("📧 Destinatarios alternativos disponibles:")
    for email in alternative_emails:
        print(f"   - {email}")
    
    print("\n💡 Puedes probar enviando a otro email para verificar si el problema")
    print("   es especí<NAME_EMAIL> o general del sistema")
    
    return alternative_emails

def main():
    print("=" * 60)
    print("DIAGNÓSTICO DE PROBLEMA DE EMAIL")
    print("=" * 60)
    
    # Verificar estado
    check_email_delivery()
    
    # Verificar configuración
    check_email_config()
    
    # Mostrar guía
    show_troubleshooting_guide()
    
    # Preguntar si quiere enviar email simple
    print(f"\n" + "=" * 40)
    try:
        respuesta = input("¿Enviar email simple de prueba? (s/n): ")
        if respuesta.lower() in ['s', 'si', 'y', 'yes']:
            test_simple_email()
    except KeyboardInterrupt:
        print("\nOperación cancelada")
    
    # Mostrar alternativas
    test_alternative_recipient()
    
    print(f"\n" + "=" * 60)
    print("📋 RESUMEN:")
    print("   - Emails técnicamente enviados (status 201)")
    print("   - Servidor Mosaico respondió 'sent'")
    print("   - Problema probablemente en entrega final")
    print("   - Revisar SPAM y filtros de email")
    print("=" * 60)

if __name__ == "__main__":
    main()
