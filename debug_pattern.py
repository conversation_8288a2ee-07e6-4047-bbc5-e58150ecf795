#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug del patrón regex
"""
import re

# Línea de ejemplo
line = '[Jul  1 13:40:22] VERBOSE[66][C-00000000] pbx.c:     -- Executing [9020@call-file-test2:46] AGI("SIP/salidacel-00000000", "reporte.py, "7040578", "calificacion", "5"") in new stack'

print("Línea de prueba:")
print(line)
print()

# Patrón base que funciona
pattern_base = r'reporte\.py, \"([^\"]*)\", \"([^\"]*)\", \"([^\"]*)\"\"\)'
print(f"Patrón base: {pattern_base}")

match = re.search(pattern_base, line)
if match:
    print(f"✅ MATCH - Grupos: {match.groups()}")
    print(f"  Grupo 1 (numero): '{match.group(1)}'")
    print(f"  Grupo 2 (accion): '{match.group(2)}'")
    print(f"  Grupo 3 (calificacion): '{match.group(3)}'")
else:
    print("❌ NO MATCH")

print()

# Patrón con placeholder
pattern_placeholder = r'reporte\.py, \"({numero_caso})\", \"([^\"]*)\", \"([^\"]*)\"\"\)'
print(f"Patrón con placeholder: {pattern_placeholder}")

# Reemplazar placeholder
pattern_final = pattern_placeholder.replace('{numero_caso}', r'([^\"]*)')
print(f"Patrón final: {pattern_final}")

match2 = re.search(pattern_final, line)
if match2:
    print(f"✅ MATCH - Grupos: {match2.groups()}")
    print(f"  Grupo 1 (numero): '{match2.group(1)}'")
    print(f"  Grupo 2 (accion): '{match2.group(2)}'")
    print(f"  Grupo 3 (calificacion): '{match2.group(3)}'")
else:
    print("❌ NO MATCH")

print()
print("=== PROBANDO LÍNEAS CON CALIFICACIÓN VACÍA ===")

line2 = '[Jul  1 13:42:48] VERBOSE[114][C-00000001] pbx.c:     -- Executing [9020@call-file-test2:46] AGI("SIP/salidacel-00000001", "reporte.py, "6997086", "calificacion", """) in new stack'
print(f"Línea 2: {line2}")

match3 = re.search(pattern_final, line2)
if match3:
    print(f"✅ MATCH - Grupos: {match3.groups()}")
    print(f"  Grupo 1 (numero): '{match3.group(1)}'")
    print(f"  Grupo 2 (accion): '{match3.group(2)}'")
    print(f"  Grupo 3 (calificacion): '{match3.group(3)}'")
else:
    print("❌ NO MATCH")
