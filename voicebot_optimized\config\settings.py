# -*- coding: utf-8 -*-
"""
Configuración centralizada del sistema Voicebot
"""
import os
from datetime import datetime

# Configuración de MongoDB
MONGODB_CONFIG = {
    'host': '***********',
    'port': 27017,
    'username': 'admin',
    'password': 'V0iC3boatT',
    'database': 'mydatabase',
    'connection_string': '********************************************/'
}

# Configuración de archivos
FILE_CONFIG = {
    'log_file_path': '../full_log',
    'temp_dir': './temp',
    'reports_dir': './reports',
    'results_json': './temp/resultados.json'
}

# Configuración de email
EMAIL_CONFIG = {
    'url': 'https://mosaico.arus.com.co:3000/mailer/withAttachments',
    'mail_type': 'divulgacion_mosaico',
    'contract': '5d682412e0a6e100062cc5cc',
    'default_recipient': '<EMAIL>',
    'sender': '<EMAIL>'
}

# Configuración de reportes
REPORT_CONFIG = {
    'column_order': [
        '_id', 'telefono', 'descripcion', 'caso_db', 'nombre', 
        'apellido', 'realizada', 'calificacion', 'finalizo', 
        'reabrir', 'fecha', 'caso', 'asignado', 'grupo'
    ],
    'default_values': {
        'calificacion': 'FALSO',
        'realizada': 'FALSO',
        'finalizo': 'FALSO'
    }
}

# Patrones de logs
LOG_PATTERNS = {
    'agi_pattern': r'AGI Tx >> "([^"]*)" "([^"]*)" "([^"]*)" "{numero_caso}"',
    'call_pattern': r'AGI Tx >> "([^"]*)" "[^"]*" "[^"]*" "{numero_caso}"'
}

def get_current_date():
    """Obtiene la fecha actual en formato YYYY-MM-DD"""
    return datetime.now().strftime('%Y-%m-%d')

def get_default_date():
    """Obtiene la fecha por defecto para consultas"""
    return "2025-06-24"  # Fecha que sabemos que tiene datos
