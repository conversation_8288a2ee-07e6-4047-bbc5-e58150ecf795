#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para limpiar archivos innecesarios del proyecto original
"""
import os
import shutil
from pathlib import Path

def cleanup_old_project():
    """Limpia archivos innecesarios del proyecto original"""
    
    # Directorio del proyecto original
    original_dir = Path("../")
    
    # Archivos y directorios a eliminar (innecesarios)
    files_to_remove = [
        # Archivos de prueba y debug
        "debug_attachment.py",
        "debug_email.py", 
        "debug_pattern.py",
        "test_alternative_email.py",
        "test_analyzer.py",
        "test_email_endpoint.py",
        "test_final.py",
        "test_processor.py",
        "test_regex.py",
        "check_email_status.py",
        "send_test_email.py",
        "send_to_gmail.py",
        
        # Scripts de automatización antiguos (reemplazados por main.py optimizado)
        "run_automation.py",
        "run_correct_automation.py", 
        "run_final_automation.py",
        "run_simple_automation.py",
        
        # Archivos obsoletos
        "py.py",  # Reemplazado por main.py
        "log_processor.py",  # Reemplazado por src/log_processor/
        "migrate.py",  # Ya no necesario
        
        # APIs (mantener solo si se necesitan)
        # "api_fastapi.py",  # Comentado - mantener si se usa
        # "api_flask.py",    # Comentado - mantener si se usa
        
        # Directorios innecesarios
        "logstash",  # Eliminado completamente
        "__pycache__",
        "temp/test_attachment.txt",
    ]
    
    # Archivos a mantener para compatibilidad
    files_to_keep = [
        "analizador.py",  # Compatibilidad
        "email_class.py",  # Compatibilidad
        "main.py",  # Versión original
        "config/",  # Configuración
        "src/",  # Código fuente
        "full_log",  # Archivo de logs
        "requirements.txt",
        "README.md",
        "RESUMEN_FINAL.md",
        "API_DOCUMENTATION.md",
        "reports/",  # Reportes generados
    ]
    
    print("🧹 LIMPIEZA DEL PROYECTO ORIGINAL")
    print("=" * 50)
    
    removed_count = 0
    kept_count = 0
    
    # Crear backup antes de eliminar
    backup_dir = original_dir / "backup_before_cleanup"
    if not backup_dir.exists():
        print(f"📦 Creando backup en: {backup_dir}")
        backup_dir.mkdir()
    
    for item in files_to_remove:
        item_path = original_dir / item
        
        if item_path.exists():
            try:
                # Hacer backup antes de eliminar
                backup_path = backup_dir / item
                if item_path.is_file():
                    backup_path.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(item_path, backup_path)
                    os.remove(item_path)
                    print(f"🗑️  Eliminado archivo: {item}")
                elif item_path.is_dir():
                    shutil.copytree(item_path, backup_path, dirs_exist_ok=True)
                    shutil.rmtree(item_path)
                    print(f"🗑️  Eliminado directorio: {item}")
                
                removed_count += 1
                
            except Exception as e:
                print(f"❌ Error eliminando {item}: {e}")
        else:
            print(f"⚠️  No encontrado: {item}")
    
    # Mostrar archivos mantenidos
    print(f"\n✅ ARCHIVOS MANTENIDOS:")
    for item in files_to_keep:
        item_path = original_dir / item
        if item_path.exists():
            if item_path.is_file():
                size = item_path.stat().st_size
                print(f"   📄 {item} ({size:,} bytes)")
            else:
                print(f"   📁 {item}/")
            kept_count += 1
    
    print(f"\n📊 RESUMEN:")
    print(f"   🗑️  Archivos eliminados: {removed_count}")
    print(f"   ✅ Archivos mantenidos: {kept_count}")
    print(f"   📦 Backup creado en: {backup_dir}")
    
    print(f"\n🎉 LIMPIEZA COMPLETADA!")
    print(f"   📁 Proyecto optimizado: voicebot_optimized/")
    print(f"   📁 Proyecto original limpio: ../")
    print(f"   📦 Backup disponible: ../backup_before_cleanup/")

if __name__ == "__main__":
    cleanup_old_project()
