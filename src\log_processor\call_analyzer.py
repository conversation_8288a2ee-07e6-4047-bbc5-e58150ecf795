# -*- coding: utf-8 -*-
"""
Analizador de llamadas para el sistema de Voicebot
"""
import json
from typing import Dict, Optional, List
from .asterisk_parser import AsteriskLogParser
from config.settings import FILE_CONFIG, REPORT_CONFIG


class AnalizadorLlamadas:
    """Clase para analizar el estado de las llamadas"""
    
    def __init__(self, log_file_path: str = None):
        self.log_file_path = log_file_path or FILE_CONFIG['log_file_path']
        self.parser = AsteriskLogParser(self.log_file_path)
        self._logs_cache = None
    
    def _cargar_logs_json(self, json_path: str = None) -> List[Dict]:
        """Carga logs desde archivo JSON si existe"""
        if self._logs_cache is not None:
            return self._logs_cache
            
        json_path = json_path or FILE_CONFIG['results_json_path']
        
        try:
            with open(json_path, "r", encoding='utf-8') as archivo:
                logs = [json.loads(line) for line in archivo if line.strip()]
            self._logs_cache = logs
            return logs
        except FileNotFoundError:
            print(f"Archivo JSON no encontrado: {json_path}")
            return []
        except Exception as e:
            print(f"Error cargando logs JSON: {e}")
            return []
    
    def analizar_llamada(self, numero_caso: str) -> Dict:
        """
        Analiza el estado de una llamada específica
        
        Args:
            numero_caso: Número de caso a analizar
            
        Returns:
            Diccionario con el estado de la llamada
        """
        resultado = {
            'numero_caso': numero_caso,
            'estado': 'No encontrado',
            'realizada': False,
            'calificacion': None,
            'accion': None,
            'finalizo': False
        }
        
        # Primero buscar en logs JSON si existen
        logs_json = self._cargar_logs_json()
        if logs_json:
            for log in logs_json:
                if log.get('numero_caso') == str(numero_caso):
                    resultado.update({
                        'estado': 'Completado',
                        'realizada': True,
                        'calificacion': log.get('calificacion'),
                        'accion': log.get('accion'),
                        'finalizo': True
                    })
                    return resultado
        
        # Si no se encuentra en JSON, buscar directamente en logs
        llamada_completa = self.parser.buscar_llamada_completa(numero_caso)
        if llamada_completa:
            numero, accion, calificacion = llamada_completa
            resultado.update({
                'estado': 'Completado',
                'realizada': True,
                'calificacion': calificacion,
                'accion': accion,
                'finalizo': True
            })
            return resultado
        
        # Si no hay llamada completa, buscar llamada iniciada
        llamada_iniciada = self.parser.buscar_llamada_iniciada(numero_caso)
        if llamada_iniciada:
            numero, accion = llamada_iniciada
            resultado.update({
                'estado': 'Iniciado',
                'realizada': True,
                'accion': accion,
                'finalizo': False
            })
            return resultado
        
        return resultado
    
    def analizar_multiples_llamadas(self, numeros_caso: List[str]) -> Dict[str, Dict]:
        """
        Analiza múltiples llamadas de una vez
        
        Args:
            numeros_caso: Lista de números de caso a analizar
            
        Returns:
            Diccionario con los resultados de cada caso
        """
        resultados = {}
        
        # Cargar logs JSON una sola vez
        logs_json = self._cargar_logs_json()
        logs_dict = {log.get('numero_caso'): log for log in logs_json}
        
        for numero_caso in numeros_caso:
            resultado = {
                'numero_caso': numero_caso,
                'estado': 'No encontrado',
                'realizada': False,
                'calificacion': None,
                'accion': None,
                'finalizo': False
            }
            
            # Buscar en logs JSON
            if str(numero_caso) in logs_dict:
                log = logs_dict[str(numero_caso)]
                resultado.update({
                    'estado': 'Completado',
                    'realizada': True,
                    'calificacion': log.get('calificacion'),
                    'accion': log.get('accion'),
                    'finalizo': True
                })
            else:
                # Buscar directamente en logs
                llamada_completa = self.parser.buscar_llamada_completa(numero_caso)
                if llamada_completa:
                    numero, accion, calificacion = llamada_completa
                    resultado.update({
                        'estado': 'Completado',
                        'realizada': True,
                        'calificacion': calificacion,
                        'accion': accion,
                        'finalizo': True
                    })
                else:
                    llamada_iniciada = self.parser.buscar_llamada_iniciada(numero_caso)
                    if llamada_iniciada:
                        numero, accion = llamada_iniciada
                        resultado.update({
                            'estado': 'Iniciado',
                            'realizada': True,
                            'accion': accion,
                            'finalizo': False
                        })
            
            resultados[numero_caso] = resultado
        
        return resultados
    
    def generar_reporte_estado(self, numeros_caso: List[str]) -> Dict:
        """
        Genera un reporte del estado de las llamadas
        
        Args:
            numeros_caso: Lista de números de caso
            
        Returns:
            Diccionario con estadísticas del reporte
        """
        resultados = self.analizar_multiples_llamadas(numeros_caso)
        
        estadisticas = {
            'total_casos': len(numeros_caso),
            'completados': 0,
            'iniciados': 0,
            'no_encontrados': 0,
            'con_calificacion': 0,
            'calificaciones_altas': 0  # Calificación > 3
        }
        
        for resultado in resultados.values():
            if resultado['estado'] == 'Completado':
                estadisticas['completados'] += 1
                if resultado['calificacion']:
                    estadisticas['con_calificacion'] += 1
                    try:
                        cal = int(resultado['calificacion'])
                        if cal > 3:
                            estadisticas['calificaciones_altas'] += 1
                    except (ValueError, TypeError):
                        pass
            elif resultado['estado'] == 'Iniciado':
                estadisticas['iniciados'] += 1
            else:
                estadisticas['no_encontrados'] += 1
        
        return {
            'estadisticas': estadisticas,
            'resultados': resultados
        }
    
    def limpiar_cache(self):
        """Limpia el cache de logs"""
        self._logs_cache = None
        self.parser.limpiar_cache()
